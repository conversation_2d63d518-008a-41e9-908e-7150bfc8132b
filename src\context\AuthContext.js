import React, { createContext, useContext, useState, useEffect } from 'react';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { Alert, Platform } from 'react-native';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Güvenlik ve Kimlik Doğrulama Context
 * PIN ve biometrik giriş yönetimi
 */
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [authSettings, setAuthSettings] = useState({
    pinEnabled: false,
    biometricEnabled: false,
    autoLockEnabled: false,
    autoLockTime: 5, // dakika
    maxAttempts: 5,
    lockoutTime: 30, // saniye
    requireBiometricFallback: true,
    showSecurityLogs: false
  });

  // Güvenlik durumu
  const [securityState, setSecurityState] = useState({
    failedAttempts: 0,
    isLockedOut: false,
    lockoutEndTime: null,
    lastAuthTime: null,
    sessionActive: false
  });

  // Güvenlik ayarları anahtarları
  const STORAGE_KEYS = {
    PIN: 'user_pin',
    PIN_ENABLED: 'pin_enabled',
    BIOMETRIC_ENABLED: 'biometric_enabled',
    AUTO_LOCK_ENABLED: 'auto_lock_enabled',
    AUTO_LOCK_TIME: 'auto_lock_time',
    LAST_AUTH_TIME: 'last_auth_time',
    MAX_ATTEMPTS: 'max_attempts',
    LOCKOUT_TIME: 'lockout_time',
    FAILED_ATTEMPTS: 'failed_attempts',
    LOCKOUT_END_TIME: 'lockout_end_time',
    SECURITY_LOGS: 'security_logs',
    REQUIRE_BIOMETRIC_FALLBACK: 'require_biometric_fallback'
  };

  // Başlangıçta ayarları yükle
  useEffect(() => {
    loadAuthSettings();
  }, []);

  // Güvenlik ayarlarını yükle
  const loadAuthSettings = async () => {
    try {
      setIsLoading(true);
      
      const [pinEnabled, biometricEnabled, autoLockEnabled, autoLockTime] = await Promise.all([
        SecureStore.getItemAsync(STORAGE_KEYS.PIN_ENABLED),
        SecureStore.getItemAsync(STORAGE_KEYS.BIOMETRIC_ENABLED),
        SecureStore.getItemAsync(STORAGE_KEYS.AUTO_LOCK_ENABLED),
        SecureStore.getItemAsync(STORAGE_KEYS.AUTO_LOCK_TIME)
      ]);

      const settings = {
        pinEnabled: pinEnabled === 'true',
        biometricEnabled: biometricEnabled === 'true',
        autoLockEnabled: autoLockEnabled === 'true',
        autoLockTime: autoLockTime ? parseInt(autoLockTime) : 5
      };

      setAuthSettings(settings);

      // Eğer güvenlik aktifse kimlik doğrulama gerekli
      if (settings.pinEnabled || settings.biometricEnabled) {
        setIsAuthenticated(false);
      } else {
        setIsAuthenticated(true);
      }

      // Güvenlik durumunu yükle
      await loadSecurityState();

      setIsLoading(false);
    } catch (error) {
      console.error('Güvenlik ayarları yükleme hatası:', error);
      setIsAuthenticated(true); // Hata durumunda erişim ver
      setIsLoading(false);
    }
  };

  // Güvenlik durumunu yükle
  const loadSecurityState = async () => {
    try {
      const failedAttempts = await SecureStore.getItemAsync(STORAGE_KEYS.FAILED_ATTEMPTS);
      const lockoutEndTime = await SecureStore.getItemAsync(STORAGE_KEYS.LOCKOUT_END_TIME);
      const lastAuthTime = await SecureStore.getItemAsync(STORAGE_KEYS.LAST_AUTH_TIME);

      const now = new Date().getTime();
      const lockoutEnd = lockoutEndTime ? parseInt(lockoutEndTime) : 0;
      const isLockedOut = lockoutEnd > now;

      setSecurityState({
        failedAttempts: parseInt(failedAttempts) || 0,
        isLockedOut,
        lockoutEndTime: isLockedOut ? lockoutEnd : null,
        lastAuthTime: lastAuthTime ? parseInt(lastAuthTime) : null,
        sessionActive: false
      });

      // Eğer lockout süresi dolmuşsa temizle
      if (lockoutEndTime && lockoutEnd <= now) {
        await clearLockout();
      }
    } catch (error) {
      console.error('Güvenlik durumu yükleme hatası:', error);
    }
  };

  // Lockout temizle
  const clearLockout = async () => {
    try {
      await SecureStore.deleteItemAsync(STORAGE_KEYS.FAILED_ATTEMPTS);
      await SecureStore.deleteItemAsync(STORAGE_KEYS.LOCKOUT_END_TIME);
      setSecurityState(prev => ({
        ...prev,
        failedAttempts: 0,
        isLockedOut: false,
        lockoutEndTime: null
      }));
    } catch (error) {
      console.error('Lockout temizleme hatası:', error);
    }
  };

  // Başarısız deneme kaydet
  const recordFailedAttempt = async () => {
    try {
      const newFailedAttempts = securityState.failedAttempts + 1;
      await SecureStore.setItemAsync(STORAGE_KEYS.FAILED_ATTEMPTS, newFailedAttempts.toString());

      // Maksimum deneme sayısına ulaşıldıysa lockout başlat
      if (newFailedAttempts >= authSettings.maxAttempts) {
        const lockoutEndTime = new Date().getTime() + (authSettings.lockoutTime * 1000);
        await SecureStore.setItemAsync(STORAGE_KEYS.LOCKOUT_END_TIME, lockoutEndTime.toString());

        setSecurityState(prev => ({
          ...prev,
          failedAttempts: newFailedAttempts,
          isLockedOut: true,
          lockoutEndTime
        }));

        await logSecurityEvent('LOCKOUT_STARTED', `${newFailedAttempts} başarısız deneme sonrası`);
        return { isLockedOut: true, lockoutTime: authSettings.lockoutTime };
      } else {
        setSecurityState(prev => ({
          ...prev,
          failedAttempts: newFailedAttempts
        }));

        await logSecurityEvent('FAILED_ATTEMPT', `Deneme ${newFailedAttempts}/${authSettings.maxAttempts}`);
        return { isLockedOut: false, remainingAttempts: authSettings.maxAttempts - newFailedAttempts };
      }
    } catch (error) {
      console.error('Başarısız deneme kaydetme hatası:', error);
      return { isLockedOut: false, remainingAttempts: authSettings.maxAttempts };
    }
  };

  // Güvenlik olayı logla
  const logSecurityEvent = async (eventType, details) => {
    try {
      if (!authSettings.showSecurityLogs) return;

      const existingLogs = await SecureStore.getItemAsync(STORAGE_KEYS.SECURITY_LOGS);
      const logs = existingLogs ? JSON.parse(existingLogs) : [];

      const newLog = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        type: eventType,
        details,
        deviceInfo: {
          platform: Platform.OS,
          timestamp: Date.now()
        }
      };

      logs.unshift(newLog);

      // Son 100 log'u tut
      const trimmedLogs = logs.slice(0, 100);

      await SecureStore.setItemAsync(STORAGE_KEYS.SECURITY_LOGS, JSON.stringify(trimmedLogs));
    } catch (error) {
      console.error('Güvenlik log hatası:', error);
    }
  };

  // PIN ayarla
  const setPin = async (pin) => {
    try {
      await SecureStore.setItemAsync(STORAGE_KEYS.PIN, pin);
      await SecureStore.setItemAsync(STORAGE_KEYS.PIN_ENABLED, 'true');
      
      setAuthSettings(prev => ({ ...prev, pinEnabled: true }));
      return { success: true };
    } catch (error) {
      console.error('PIN ayarlama hatası:', error);
      return { success: false, error: 'PIN ayarlanamadı' };
    }
  };

  // PIN kaldır
  const removePin = async () => {
    try {
      await SecureStore.deleteItemAsync(STORAGE_KEYS.PIN);
      await SecureStore.setItemAsync(STORAGE_KEYS.PIN_ENABLED, 'false');
      
      setAuthSettings(prev => ({ ...prev, pinEnabled: false }));
      return { success: true };
    } catch (error) {
      console.error('PIN kaldırma hatası:', error);
      return { success: false, error: 'PIN kaldırılamadı' };
    }
  };

  // PIN doğrula (gelişmiş güvenlik ile)
  const verifyPin = async (inputPin) => {
    try {
      // Lockout kontrolü
      if (securityState.isLockedOut) {
        const remainingTime = Math.ceil((securityState.lockoutEndTime - new Date().getTime()) / 1000);
        return {
          success: false,
          isLockedOut: true,
          remainingTime,
          message: `${remainingTime} saniye sonra tekrar deneyin`
        };
      }

      const storedPin = await SecureStore.getItemAsync(STORAGE_KEYS.PIN);
      const isValid = storedPin === inputPin;

      if (isValid) {
        // Başarılı giriş - güvenlik durumunu temizle
        await clearLockout();
        await logSecurityEvent('PIN_SUCCESS', 'PIN ile başarılı giriş');

        // Son kimlik doğrulama zamanını güncelle
        const now = new Date().getTime();
        await SecureStore.setItemAsync(STORAGE_KEYS.LAST_AUTH_TIME, now.toString());
        setSecurityState(prev => ({ ...prev, lastAuthTime: now, sessionActive: true }));

        return { success: true };
      } else {
        // Başarısız giriş
        const result = await recordFailedAttempt();
        await logSecurityEvent('PIN_FAILED', `Hatalı PIN girişi`);

        return {
          success: false,
          isLockedOut: result.isLockedOut,
          remainingAttempts: result.remainingAttempts,
          lockoutTime: result.lockoutTime,
          message: result.isLockedOut
            ? `Çok fazla hatalı deneme. ${result.lockoutTime} saniye bekleyin.`
            : `Hatalı PIN. Kalan deneme: ${result.remainingAttempts}`
        };
      }
    } catch (error) {
      console.error('PIN doğrulama hatası:', error);
      await logSecurityEvent('PIN_ERROR', `PIN doğrulama hatası: ${error.message}`);
      return { success: false, message: 'PIN doğrulama hatası' };
    }
  };

  // Biometrik ayarları
  const setBiometric = async (enabled) => {
    try {
      if (enabled) {
        // Biometrik desteği kontrol et
        const hasHardware = await LocalAuthentication.hasHardwareAsync();
        const isEnrolled = await LocalAuthentication.isEnrolledAsync();
        
        if (!hasHardware) {
          return { success: false, error: 'Cihazınız biometrik kimlik doğrulamayı desteklemiyor' };
        }
        
        if (!isEnrolled) {
          return { success: false, error: 'Cihazınızda kayıtlı biometrik veri bulunmuyor' };
        }
      }

      await SecureStore.setItemAsync(STORAGE_KEYS.BIOMETRIC_ENABLED, enabled.toString());
      setAuthSettings(prev => ({ ...prev, biometricEnabled: enabled }));
      
      return { success: true };
    } catch (error) {
      console.error('Biometrik ayarlama hatası:', error);
      return { success: false, error: 'Biometrik ayarlar güncellenemedi' };
    }
  };

  // Biometrik kimlik doğrulama (gelişmiş)
  const authenticateWithBiometric = async () => {
    try {
      // Lockout kontrolü
      if (securityState.isLockedOut) {
        const remainingTime = Math.ceil((securityState.lockoutEndTime - new Date().getTime()) / 1000);
        return {
          success: false,
          isLockedOut: true,
          remainingTime,
          message: `${remainingTime} saniye sonra tekrar deneyin`
        };
      }

      // Biometric uygunluk kontrolü
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (!hasHardware) {
        return { success: false, message: 'Biometric donanım bulunamadı' };
      }

      if (!isEnrolled) {
        return { success: false, message: 'Biometric kayıt bulunamadı' };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Uygulamaya erişmek için kimliğinizi doğrulayın',
        cancelLabel: 'İptal',
        fallbackLabel: authSettings.requireBiometricFallback ? 'PIN kullan' : undefined,
        disableDeviceFallback: !authSettings.requireBiometricFallback,
      });

      if (result.success) {
        // Başarılı giriş
        await clearLockout();
        await logSecurityEvent('BIOMETRIC_SUCCESS', 'Biometric ile başarılı giriş');

        // Son kimlik doğrulama zamanını güncelle
        const now = new Date().getTime();
        await SecureStore.setItemAsync(STORAGE_KEYS.LAST_AUTH_TIME, now.toString());
        setSecurityState(prev => ({ ...prev, lastAuthTime: now, sessionActive: true }));

        return { success: true };
      }

      // Başarısız giriş
      if (result.error === 'UserCancel') {
        await logSecurityEvent('BIOMETRIC_CANCELLED', 'Kullanıcı biometric kimlik doğrulamayı iptal etti');
        return { success: false, cancelled: true, message: 'Kimlik doğrulama iptal edildi' };
      }

      const failResult = await recordFailedAttempt();
      await logSecurityEvent('BIOMETRIC_FAILED', `Biometric kimlik doğrulama başarısız: ${result.error}`);

      return {
        success: false,
        isLockedOut: failResult.isLockedOut,
        remainingAttempts: failResult.remainingAttempts,
        lockoutTime: failResult.lockoutTime,
        message: failResult.isLockedOut
          ? `Çok fazla hatalı deneme. ${failResult.lockoutTime} saniye bekleyin.`
          : `Kimlik doğrulama başarısız. Kalan deneme: ${failResult.remainingAttempts}`
      };
    } catch (error) {
      console.error('Biometric kimlik doğrulama hatası:', error);
      await logSecurityEvent('BIOMETRIC_ERROR', `Biometric hatası: ${error.message}`);
      return { success: false, message: 'Biometric kimlik doğrulama hatası' };
    }
  };

  // Otomatik kilit ayarları
  const setAutoLock = async (enabled, timeInMinutes = 5) => {
    try {
      await SecureStore.setItemAsync(STORAGE_KEYS.AUTO_LOCK_ENABLED, enabled.toString());
      await SecureStore.setItemAsync(STORAGE_KEYS.AUTO_LOCK_TIME, timeInMinutes.toString());
      
      setAuthSettings(prev => ({ 
        ...prev, 
        autoLockEnabled: enabled, 
        autoLockTime: timeInMinutes 
      }));
      
      return { success: true };
    } catch (error) {
      console.error('Otomatik kilit ayarlama hatası:', error);
      return { success: false, error: 'Otomatik kilit ayarları güncellenemedi' };
    }
  };

  // Kimlik doğrulama başarılı
  const authenticate = async () => {
    try {
      setIsAuthenticated(true);
      await SecureStore.setItemAsync(STORAGE_KEYS.LAST_AUTH_TIME, Date.now().toString());
    } catch (error) {
      console.error('Kimlik doğrulama kaydetme hatası:', error);
    }
  };

  // Çıkış yap (kilitle)
  const logout = () => {
    setIsAuthenticated(false);
  };

  // Biometrik desteği kontrol et
  const checkBiometricSupport = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      return {
        hasHardware,
        isEnrolled,
        supportedTypes,
        isSupported: hasHardware && isEnrolled
      };
    } catch (error) {
      console.error('Biometrik destek kontrolü hatası:', error);
      return {
        hasHardware: false,
        isEnrolled: false,
        supportedTypes: [],
        isSupported: false
      };
    }
  };

  // Tam kimlik doğrulama (PIN veya biometrik)
  const performAuthentication = async () => {
    try {
      // Önce biometrik dene (eğer aktifse)
      if (authSettings.biometricEnabled) {
        const biometricResult = await authenticateWithBiometric();
        if (biometricResult) {
          await authenticate();
          return { success: true, method: 'biometric' };
        }
      }

      // Biometrik başarısızsa veya aktif değilse PIN'e yönlendir
      if (authSettings.pinEnabled) {
        return { success: false, method: 'pin', requirePin: true };
      }

      // Hiçbir güvenlik yoksa direkt geçir
      await authenticate();
      return { success: true, method: 'none' };
    } catch (error) {
      console.error('Kimlik doğrulama hatası:', error);
      return { success: false, error: 'Kimlik doğrulama başarısız' };
    }
  };

  const value = {
    isAuthenticated,
    isLoading,
    authSettings,
    securityState,
    setPin,
    removePin,
    verifyPin,
    setBiometric,
    authenticateWithBiometric,
    setAutoLock,
    authenticate,
    logout,
    checkBiometricSupport,
    performAuthentication,
    loadAuthSettings,
    recordFailedAttempt,
    clearLockout,
    logSecurityEvent,
    loadSecurityState
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
