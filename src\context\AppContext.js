import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSQLiteContext } from 'expo-sqlite';
import { Appearance } from 'react-native';
import * as settingsService from '../services/settingsService';
import { Colors, lightTheme, darkTheme } from '../constants/colors';
import tokens from '../design-system/tokens';
import globalStyles from '../design-system/globalStyles';

// AppContext - Uygulama genelinde kullanılacak değerler için merkezi depo
const AppContext = createContext({
  theme: Colors,
  defaultCurrency: 'TRY',
  isDarkMode: false,
  toggleTheme: () => {},
});

/**
 * Ana uygulama context sağlayıcısı
 * Tema ve para birimi gibi uygulama genelindeki ayarları yönetir
 *
 * @param {Object} props - Bileşen props'ları
 * @param {React.ReactNode} props.children - Alt bileşenler
 * @returns {JSX.Element} AppProvider bileşeni
 */
export const AppProvider = ({ children }) => {
  const db = useSQLiteContext();
  const [defaultCurrency, setDefaultCurrency] = useState('TRY');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [themePreference, setThemePreference] = useState('system'); // 'light', 'dark', 'system'

  // Tema ayarlarını yükle
  useEffect(() => {
    const loadThemeSettings = async () => {
      try {
        const savedThemePreference = await AsyncStorage.getItem('themePreference');
        if (savedThemePreference) {
          setThemePreference(savedThemePreference);
        }

        // Sistem temasını kontrol et
        const systemColorScheme = Appearance.getColorScheme();
        const shouldUseDarkMode = savedThemePreference === 'dark' ||
          (savedThemePreference === 'system' && systemColorScheme === 'dark');

        setIsDarkMode(shouldUseDarkMode);
      } catch (error) {
        console.error('Tema ayarları yüklenirken hata:', error);
      }
    };

    loadThemeSettings();

    // Sistem tema değişikliklerini dinle
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (themePreference === 'system') {
        setIsDarkMode(colorScheme === 'dark');
      }
    });

    return () => subscription?.remove();
  }, [themePreference]);

  // Varsayılan para birimini yükle
  useEffect(() => {
    const loadDefaultCurrency = async () => {
      try {
        // Önce AsyncStorage'den kontrol et
        const storedCurrency = await AsyncStorage.getItem('defaultCurrency');

        if (storedCurrency) {
          setDefaultCurrency(storedCurrency);
          return;
        }

        // Veritabanından kontrol et (eğer settingsService mevcutsa)
        if (db && settingsService.getSetting) {
          const defaultCurrencySetting = await settingsService.getSetting(db, 'defaultCurrency');
          if (defaultCurrencySetting) {
            setDefaultCurrency(defaultCurrencySetting);

            // AsyncStorage'e de kaydet
            await AsyncStorage.setItem('defaultCurrency', defaultCurrencySetting);
          }
        }
      } catch (error) {
        console.error('Varsayılan para birimi yüklenirken hata:', error);
      }
    };

    loadDefaultCurrency();
  }, [db]);

  // Tema değiştirme fonksiyonu
  const toggleTheme = async (newThemePreference) => {
    try {
      setThemePreference(newThemePreference);
      await AsyncStorage.setItem('themePreference', newThemePreference);

      if (newThemePreference === 'system') {
        const systemColorScheme = Appearance.getColorScheme();
        setIsDarkMode(systemColorScheme === 'dark');
      } else {
        setIsDarkMode(newThemePreference === 'dark');
      }
    } catch (error) {
      console.error('Tema değiştirilirken hata:', error);
    }
  };

  // Mevcut tema renklerini al
  const currentTheme = isDarkMode ? darkTheme : lightTheme;

  // Context değeri
  const value = {
    theme: currentTheme,
    defaultCurrency,
    isDarkMode,
    themePreference,
    toggleTheme,
    tokens,
    globalStyles,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

/**
 * Uygulama context'ini kullanmak için hook
 * @returns {Object} AppContext değerleri
 */
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
