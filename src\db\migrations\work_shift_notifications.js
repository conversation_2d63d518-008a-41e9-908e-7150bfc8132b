/**
 * Vardiya bildirimleri tablosunu oluşturan migrasyon
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftNotifications = async (db) => {
  try {
    // Migrasyon işlemini bir transaction içinde yap
    await db.execAsync('BEGIN TRANSACTION;');

    // Tablo var mı kontrol et
    const tableExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='work_shift_notifications'
    `);

    if (!tableExists) {
      // Tablo yoksa oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS work_shift_notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          shift_id INTEGER,
          schedule_id INTEGER,
          notification_time TEXT NOT NULL,
          is_sent INTEGER DEFAULT 0,
          title TEXT NOT NULL,
          body TEXT NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (shift_id) REFERENCES work_shifts (id) ON DELETE CASCADE,
          FOREIGN KEY (schedule_id) REFERENCES work_shift_schedules (id) ON DELETE CASCADE
        )
      `);
      
      // İndeks oluştur
      await db.execAsync(`
        CREATE INDEX IF NOT EXISTS idx_work_shift_notifications_shift_id ON work_shift_notifications (shift_id);
        CREATE INDEX IF NOT EXISTS idx_work_shift_notifications_schedule_id ON work_shift_notifications (schedule_id);
        CREATE INDEX IF NOT EXISTS idx_work_shift_notifications_notification_time ON work_shift_notifications (notification_time);
        CREATE INDEX IF NOT EXISTS idx_work_shift_notifications_is_sent ON work_shift_notifications (is_sent);
      `);
    }

    // Transaction'ı tamamla
    await db.execAsync('COMMIT;');
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await db.execAsync('ROLLBACK;');
    console.error('Vardiya bildirimleri migrasyonu hatası:', error);
    throw error;
  }
};
