import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Basit kategori seçici bileşeni
 * @param {Object} props Component props
 * @param {Array} props.categories Kategori listesi
 * @param {number|null} props.selectedCategoryId Seçili kategori ID'si
 * @param {Function} props.onSelectCategory Kategori seçildiğinde çağrılacak fonksiyon
 * @param {Function} props.onAddCategory Yeni kategori ekle butonuna tıklandığında çağrılacak fonksiyon
 * @param {string} props.type Kategori tipi ('income', 'expense', 'both')
 */
const CategorySelectorSimple = ({
  categories,
  selectedCategoryId,
  onSelectCategory,
  onAddCategory,
  type = 'both'
}) => {
  return (
    <View style={styles.wrapper}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Kategori</Text>
        {onAddCategory && (
          <TouchableOpacity
            style={styles.addCategoryButtonSmall}
            onPress={() => onAddCategory(type)}
          >
            <MaterialIcons name="add" size={16} color={Colors.PRIMARY} />
            <Text style={styles.addCategoryTextSmall}>Yeni</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryItem,
              selectedCategoryId === category.id && {
                backgroundColor: category.color || Colors.PRIMARY
              }
            ]}
            onPress={() => onSelectCategory(category.id)}
          >
            <View style={styles.categoryIconContainer}>
              <MaterialIcons
                name={category.icon || 'category'}
                size={24}
                color={selectedCategoryId === category.id ? '#fff' : category.color || Colors.PRIMARY}
              />
            </View>
            <Text
              style={[
                styles.categoryText,
                selectedCategoryId === category.id && styles.activeCategoryText
              ]}
              numberOfLines={1}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}

        {/* Yeni Kategori Butonu */}
        {onAddCategory && (
          <TouchableOpacity
            style={styles.addCategoryButton}
            onPress={() => onAddCategory(type)}
          >
            <View style={styles.addIconContainer}>
              <MaterialIcons
                name="add"
                size={24}
                color={Colors.PRIMARY}
              />
            </View>
            <Text style={styles.addCategoryText}>
              Yeni Kategori
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
  },
  addCategoryButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f0f8ff',
    borderWidth: 1,
    borderColor: Colors.PRIMARY_LIGHT || '#d0e8f2',
  },
  addCategoryTextSmall: {
    fontSize: 12,
    color: Colors.PRIMARY,
    marginLeft: 2,
  },
  container: {
    flexDirection: 'row',
  },
  scrollContent: {
    paddingVertical: 4,
  },
  categoryItem: {
    width: 80,
    alignItems: 'center',
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    marginRight: 8,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
    paddingHorizontal: 4,
    width: '100%',
  },
  activeCategoryText: {
    color: '#fff',
  },
  addCategoryButton: {
    width: 80,
    alignItems: 'center',
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: Colors.PRIMARY_LIGHT || '#d0e8f2',
    borderRadius: 12,
    marginRight: 8,
    backgroundColor: '#f0f8ff',
  },
  addIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e6f7ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  addCategoryText: {
    fontSize: 12,
    color: Colors.PRIMARY,
    textAlign: 'center',
    paddingHorizontal: 4,
    width: '100%',
  },
});

export default CategorySelectorSimple;
