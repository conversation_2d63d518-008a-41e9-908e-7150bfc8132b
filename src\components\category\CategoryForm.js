import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Modal, ScrollView, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../../constants/colors';
import { useAppContext } from '../../context/AppContext';
import ColorSelector from './ColorSelector';
import IconSelector from './IconSelector';

/**
 * Kategori formu bileşeni
 * @param {Object} props Component props
 * @param {boolean} props.visible Modal görünürlüğü
 * @param {Function} props.onClose Modal kapatma fonksiyonu
 * @param {Object} props.category Düzenlenecek kategori (yoksa yeni kategori)
 * @param {string} props.type Kategori tipi ('income', 'expense', 'both')
 * @param {Function} props.onSave Kategori kaydetme fonksiyonu
 */
const CategoryForm = ({ visible, onClose, category, type = 'both', onSave }) => {
  const db = useSQLiteContext();
  const { theme } = useAppContext();

  const [name, setName] = useState('');
  const [selectedType, setSelectedType] = useState(type);
  const [selectedColor, setSelectedColor] = useState('#3498db');
  const [selectedIcon, setSelectedIcon] = useState('category');
  const [showColorSelector, setShowColorSelector] = useState(false);
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [isDefault, setIsDefault] = useState(false);

  // Kategori düzenleme durumunda verileri yükle
  useEffect(() => {
    if (category) {
      setName(category.name || '');
      setSelectedType(category.type || type);
      setSelectedColor(category.color || '#3498db');
      setSelectedIcon(category.icon || 'category');
      setIsDefault(category.is_default === 1);
    } else {
      // Yeni kategori
      setName('');
      setSelectedType(type);
      setSelectedColor('#3498db');
      setSelectedIcon('category');
      setIsDefault(false);
    }
  }, [category, type]);

  // Renk seçici modalını aç/kapat
  const toggleColorSelector = () => {
    setShowColorSelector(!showColorSelector);
  };

  // İkon seçici modalını aç/kapat
  const toggleIconSelector = () => {
    setShowIconSelector(!showIconSelector);
  };

  // Renk seç
  const selectColor = (color) => {
    setSelectedColor(color);
    setShowColorSelector(false);
  };

  // İkon seç
  const selectIcon = (icon) => {
    setSelectedIcon(icon);
    setShowIconSelector(false);
  };

  // Kategori tipini değiştir
  const changeType = (type) => {
    setSelectedType(type);
  };

  // Kategoriyi kaydet
  const saveCategory = async () => {
    try {
      // Validasyon
      if (!name.trim()) {
        Alert.alert('Hata', 'Kategori adı boş olamaz');
        return;
      }

      // Kategori verilerini hazırla
      const categoryData = {
        name: name.trim(),
        type: selectedType,
        color: selectedColor,
        icon: selectedIcon,
        is_default: isDefault ? 1 : 0
      };

      // Kategori düzenleme
      if (category && category.id) {
        await db.runAsync(`
          UPDATE categories
          SET name = ?, type = ?, color = ?, icon = ?, is_default = ?
          WHERE id = ?
        `, [
          categoryData.name,
          categoryData.type,
          categoryData.color,
          categoryData.icon,
          categoryData.is_default,
          category.id
        ]);

        // Eğer varsayılan kategori olarak işaretlendiyse, diğer varsayılan kategorileri güncelle
        if (categoryData.is_default) {
          await db.runAsync(`
            UPDATE categories
            SET is_default = 0
            WHERE id != ? AND type = ?
          `, [category.id, categoryData.type]);
        }

        if (onSave) onSave(categoryData);
        onClose();
      }
      // Yeni kategori ekleme
      else {
        // Eğer varsayılan kategori olarak işaretlendiyse, diğer varsayılan kategorileri güncelle
        if (categoryData.is_default) {
          await db.runAsync(`
            UPDATE categories
            SET is_default = 0
            WHERE type = ?
          `, [categoryData.type]);
        }

        const result = await db.runAsync(`
          INSERT INTO categories (name, type, color, icon, is_default)
          VALUES (?, ?, ?, ?, ?)
        `, [
          categoryData.name,
          categoryData.type,
          categoryData.color,
          categoryData.icon,
          categoryData.is_default
        ]);

        categoryData.id = result.lastInsertRowId;

        if (onSave) onSave(categoryData);
        onClose();
      }
    } catch (error) {
      console.error('Kategori kaydetme hatası:', error);
      Alert.alert('Hata', 'Kategori kaydedilirken bir hata oluştu');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
          <View style={[styles.modalHeader, { borderBottomColor: theme.BORDER }]}>
            <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
              {category ? 'Kategori Düzenle' : 'Yeni Kategori'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.formContainer}>
            {/* Kategori Önizleme */}
            <View style={styles.previewContainer}>
              <View style={[styles.previewIcon, { backgroundColor: selectedColor }]}>
                <MaterialIcons name={selectedIcon} size={32} color="#fff" />
              </View>
              <Text style={styles.previewName}>
                {name || 'Yeni Kategori'}
              </Text>
              <View style={[styles.previewType, {
                backgroundColor: selectedType === 'income'
                  ? Colors.SUCCESS
                  : selectedType === 'expense'
                    ? Colors.DANGER
                    : Colors.WARNING
              }]}>
                <Text style={styles.previewTypeText}>
                  {selectedType === 'income'
                    ? 'Gelir'
                    : selectedType === 'expense'
                      ? 'Gider'
                      : 'Her İkisi'}
                </Text>
              </View>
            </View>

            {/* Kategori Adı */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Kategori Adı</Text>
              <TextInput
                style={styles.textInput}
                value={name}
                onChangeText={setName}
                placeholder="Kategori adı girin"
              />
            </View>

            {/* Kategori Tipi */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Kategori Tipi</Text>
              <View style={styles.typeContainer}>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    selectedType === 'income' && styles.activeTypeButton
                  ]}
                  onPress={() => changeType('income')}
                >
                  <MaterialIcons
                    name="arrow-upward"
                    size={18}
                    color={selectedType === 'income' ? '#fff' : Colors.SUCCESS}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      selectedType === 'income' && styles.activeTypeButtonText
                    ]}
                  >
                    Gelir
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    selectedType === 'expense' && styles.activeTypeButton,
                    selectedType === 'expense' && { backgroundColor: Colors.DANGER }
                  ]}
                  onPress={() => changeType('expense')}
                >
                  <MaterialIcons
                    name="arrow-downward"
                    size={18}
                    color={selectedType === 'expense' ? '#fff' : Colors.DANGER}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      selectedType === 'expense' && styles.activeTypeButtonText
                    ]}
                  >
                    Gider
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    selectedType === 'both' && styles.activeTypeButton,
                    selectedType === 'both' && { backgroundColor: Colors.WARNING }
                  ]}
                  onPress={() => changeType('both')}
                >
                  <MaterialIcons
                    name="swap-vert"
                    size={18}
                    color={selectedType === 'both' ? '#fff' : Colors.WARNING}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      selectedType === 'both' && styles.activeTypeButtonText
                    ]}
                  >
                    Her İkisi
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.rowContainer}>
              {/* Renk Seçimi */}
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.inputLabel}>Renk</Text>
                <TouchableOpacity
                  style={styles.colorButton}
                  onPress={toggleColorSelector}
                >
                  <View style={[styles.colorPreview, { backgroundColor: selectedColor }]} />
                  <Text style={styles.colorButtonText}>{selectedColor}</Text>
                  <MaterialIcons name="palette" size={20} color="#666" />
                </TouchableOpacity>
              </View>

              {/* İkon Seçimi */}
              <View style={[styles.inputContainer, { flex: 1 }]}>
                <Text style={styles.inputLabel}>İkon</Text>
                <TouchableOpacity
                  style={styles.iconButton}
                  onPress={toggleIconSelector}
                >
                  <MaterialIcons name={selectedIcon} size={24} color={selectedColor} />
                  <Text style={styles.iconButtonText}>{selectedIcon}</Text>
                  <MaterialIcons name="more-horiz" size={20} color="#666" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Varsayılan Kategori */}
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setIsDefault(!isDefault)}
              >
                <MaterialIcons
                  name={isDefault ? 'check-box' : 'check-box-outline-blank'}
                  size={24}
                  color={Colors.PRIMARY}
                />
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>
                Varsayılan kategori olarak ayarla
              </Text>
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.saveButton}
              onPress={saveCategory}
            >
              <Text style={styles.saveButtonText}>Kaydet</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Renk Seçici Modal */}
      <ColorSelector
        visible={showColorSelector}
        onClose={toggleColorSelector}
        onSelectColor={selectColor}
        selectedColor={selectedColor}
      />

      {/* İkon Seçici Modal */}
      <IconSelector
        visible={showIconSelector}
        onClose={toggleIconSelector}
        onSelectIcon={selectIcon}
        selectedIcon={selectedIcon}
        color={selectedColor}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.BLACK,
  },
  formContainer: {
    padding: 16,
  },
  previewContainer: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
  },
  previewIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  previewName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
    marginBottom: 8,
  },
  previewType: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  previewTypeText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 12,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: Colors.GRAY_800,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  typeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
    justifyContent: 'center',
  },
  activeTypeButton: {
    backgroundColor: Colors.SUCCESS,
    borderColor: Colors.SUCCESS,
  },
  typeButtonText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  activeTypeButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  colorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
  },
  colorPreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  colorButtonText: {
    flex: 1,
    fontSize: 16,
    color: '#666',
  },
  iconButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
  },
  iconButtonText: {
    flex: 1,
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  checkbox: {
    marginRight: 8,
  },
  checkboxLabel: {
    fontSize: 16,
    color: Colors.GRAY_800,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: Colors.GRAY_800,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
});

export default CategoryForm;
