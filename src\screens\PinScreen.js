import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { pinService } from '../services/pinService';

/**
 * PIN ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const PinScreen = ({ navigation, route }) => {
  const insets = useSafeAreaInsets();
  const { mode = 'verify' } = route.params || {};

  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState(1);
  const [error, setError] = useState('');

  // PIN doğrulama
  const verifyPin = async () => {
    try {
      const isValid = await pinService.verifyPin(pin);

      if (isValid) {
        if (mode === 'change') {
          // PIN değiştirme modunda, yeni PIN'e geç
          setStep(2);
          setPin('');
        } else {
          // Doğrulama başarılı, ana ekrana git
          navigation.replace('Main');
        }
      } else {
        setError('Yanlış PIN kodu');
        setPin('');
      }
    } catch (error) {
      console.error('PIN doğrulama hatası:', error);
      setError('PIN doğrulanırken bir hata oluştu');
      setPin('');
    }
  };

  // PIN ayarlama
  const setupPin = async () => {
    if (step === 1) {
      // İlk adım tamamlandı, doğrulama adımına geç
      setStep(2);
      setConfirmPin('');
    } else {
      // PIN'leri karşılaştır
      if (pin === confirmPin) {
        try {
          await pinService.setPin(pin);

          if (mode === 'change') {
            navigation.goBack();
          } else {
            navigation.replace('Main');
          }
        } catch (error) {
          console.error('PIN ayarlama hatası:', error);
          setError('PIN ayarlanırken bir hata oluştu');
          setPin('');
          setConfirmPin('');
          setStep(1);
        }
      } else {
        setError('PIN kodları eşleşmiyor');
        setConfirmPin('');
      }
    }
  };

  // PIN'e rakam ekle
  const appendToPin = (digit) => {
    if (step === 1) {
      if (pin.length < 4) {
        setPin(pin + digit);
        setError('');
      }
    } else {
      if (confirmPin.length < 4) {
        setConfirmPin(confirmPin + digit);
        setError('');
      }
    }
  };

  // Son rakamı sil
  const deleteLastDigit = () => {
    if (step === 1) {
      setPin(pin.slice(0, -1));
    } else {
      setConfirmPin(confirmPin.slice(0, -1));
    }
    setError('');
  };

  // PIN tamamlandığında
  useEffect(() => {
    if (mode === 'verify' && pin.length === 4) {
      verifyPin();
    } else if ((mode === 'setup' || mode === 'change') && step === 1 && pin.length === 4) {
      setupPin();
    } else if ((mode === 'setup' || mode === 'change') && step === 2 && confirmPin.length === 4) {
      setupPin();
    }
  }, [pin, confirmPin]);

  // Başlık metni
  const getTitle = () => {
    if (mode === 'verify') {
      return 'PIN Kodunu Girin';
    } else if (mode === 'setup') {
      return step === 1 ? 'PIN Kodu Oluşturun' : 'PIN Kodunu Doğrulayın';
    } else if (mode === 'change') {
      return step === 1 ? 'Mevcut PIN Kodunu Girin' : 'Yeni PIN Kodu Oluşturun';
    }
  };

  // Alt metin
  const getSubtitle = () => {
    if (mode === 'verify') {
      return 'Lütfen güvenlik PIN kodunuzu girin';
    } else if (mode === 'setup') {
      return step === 1
        ? 'Lütfen 4 haneli bir PIN kodu oluşturun'
        : 'Lütfen PIN kodunuzu tekrar girin';
    } else if (mode === 'change') {
      return step === 1
        ? 'Lütfen mevcut PIN kodunuzu girin'
        : 'Lütfen yeni PIN kodunuzu oluşturun';
    }
  };

  // PIN noktaları
  const renderPinDots = () => {
    const currentPin = step === 1 ? pin : confirmPin;
    const dots = [];

    for (let i = 0; i < 4; i++) {
      dots.push(
        <View
          key={i}
          style={[
            styles.pinDot,
            i < currentPin.length && styles.pinDotFilled
          ]}
        />
      );
    }

    return <View style={styles.pinDotsContainer}>{dots}</View>;
  };

  // Numara tuşları
  const renderNumberPad = () => {
    const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0];

    return (
      <View style={styles.numberPad}>
        {numbers.map((number, index) => (
          <TouchableOpacity
            key={number}
            style={[
              styles.numberButton,
              number === 0 && { marginLeft: '33.33%' }
            ]}
            onPress={() => appendToPin(number.toString())}
          >
            <Text style={styles.numberText}>{number}</Text>
          </TouchableOpacity>
        ))}

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={deleteLastDigit}
        >
          <MaterialIcons name="backspace" size={24} color="#666" />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        {mode !== 'verify' && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>{getTitle()}</Text>
        <Text style={styles.subtitle}>{getSubtitle()}</Text>

        {renderPinDots()}

        {error ? <Text style={styles.errorText}>{error}</Text> : null}
      </View>

      {renderNumberPad()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#999',
    marginBottom: 32,
    textAlign: 'center',
  },
  pinDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    marginHorizontal: 8,
  },
  pinDotFilled: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  errorText: {
    color: '#e74c3c',
    marginTop: 16,
    textAlign: 'center',
  },
  numberPad: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    padding: 16,
  },
  numberButton: {
    width: '33.33%',
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  numberText: {
    fontSize: 28,
    fontWeight: '500',
    color: '#333',
  },
  deleteButton: {
    width: '33.33%',
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PinScreen;
