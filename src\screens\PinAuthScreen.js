import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Vibration,
  Alert,
  Animated,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';

/**
 * PIN Kimlik Doğrulama Ekranı
 * 4 haneli PIN ile giriş
 */
export default function PinAuthScreen({ navigation, route }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  const {
    verifyPin,
    authenticate,
    authenticateWithBiometric,
    authSettings,
    securityState,
    setPin: savePin
  } = useAuth();

  const [pin, setPin] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isLockedOut, setIsLockedOut] = useState(false);
  const [lockoutTime, setLockoutTime] = useState(0);
  const [errorMessage, setErrorMessage] = useState('');
  const [remainingAttempts, setRemainingAttempts] = useState(5);

  // Animasyon referansları
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(1)).current;

  const mode = route?.params?.mode || 'auth'; // 'auth', 'setup', 'change'
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState(mode === 'setup' ? 'enter' : 'auth'); // 'enter', 'confirm', 'auth'

  const maxAttempts = 5;

  // Biometrik giriş dene
  useEffect(() => {
    if (mode === 'auth' && authSettings.biometricEnabled && !isLockedOut) {
      tryBiometricAuth();
    }
  }, []);

  // Lockout timer
  const startLockoutTimer = (seconds) => {
    const timer = setInterval(() => {
      setLockoutTime(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          setIsLockedOut(false);
          setErrorMessage('');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const tryBiometricAuth = async () => {
    try {
      setIsLoading(true);
      const result = await authenticateWithBiometric();

      if (result.success) {
        await authenticate();
        Vibration.vibrate(50); // Başarı titreşimi
      } else if (result.isLockedOut) {
        setIsLockedOut(true);
        setLockoutTime(result.remainingTime);
        setErrorMessage(result.message);
        startLockoutTimer(result.remainingTime);
      } else if (result.cancelled) {
        // Kullanıcı iptal etti, hiçbir şey yapma
      } else {
        setErrorMessage(result.message || 'Biometric kimlik doğrulama başarısız');
        if (result.remainingAttempts !== undefined) {
          setRemainingAttempts(result.remainingAttempts);
        }
        Vibration.vibrate([100, 50, 100]); // Hata titreşimi
      }
    } catch (error) {
      console.error('Biometrik giriş hatası:', error);
      setErrorMessage('Biometric kimlik doğrulama hatası');
    } finally {
      setIsLoading(false);
    }
  };

  // PIN girişi
  const handleNumberPress = (number) => {
    if (pin.length < 4) {
      const newPin = pin + number;
      setPin(newPin);

      // 4 hane tamamlandığında işlem yap
      if (newPin.length === 4) {
        setTimeout(() => {
          handlePinComplete(newPin);
        }, 100);
      }
    }
  };

  // PIN silme
  const handleBackspace = () => {
    setPin(pin.slice(0, -1));
  };

  // PIN tamamlandığında
  const handlePinComplete = async (completedPin) => {
    setIsLoading(true);

    try {
      if (mode === 'auth') {
        // Kimlik doğrulama modu
        const result = await verifyPin(completedPin);

        if (result.success) {
          await authenticate();
          Vibration.vibrate(50); // Başarı titreşimi
        } else if (result.isLockedOut) {
          setIsLockedOut(true);
          setLockoutTime(result.remainingTime || result.lockoutTime);
          setErrorMessage(result.message);
          startLockoutTimer(result.remainingTime || result.lockoutTime);
          Vibration.vibrate([100, 50, 100, 50, 100]); // Uzun hata titreşimi
        } else {
          // Başarısız giriş
          if (result.remainingAttempts !== undefined) {
            setRemainingAttempts(result.remainingAttempts);
          }
          setErrorMessage(result.message);
          Vibration.vibrate([100, 50, 100]); // Hata titreşimi

          // Shake animasyonu
          Animated.sequence([
            Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
            Animated.timing(shakeAnimation, { toValue: -10, duration: 100, useNativeDriver: true }),
            Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
            Animated.timing(shakeAnimation, { toValue: 0, duration: 100, useNativeDriver: true })
          ]).start();
        }

        setPin('');
      } else if (mode === 'setup') {
        // PIN kurulum modu
        if (step === 'enter') {
          setConfirmPin(completedPin);
          setPin('');
          setStep('confirm');
        } else if (step === 'confirm') {
          if (completedPin === confirmPin) {
            // PIN'ler eşleşiyor, kaydet
            const result = await setPin(completedPin);
            if (result.success) {
              Alert.alert(
                'Başarılı',
                'PIN başarıyla ayarlandı',
                [{ text: 'Tamam', onPress: () => navigation.goBack() }]
              );
            } else {
              Alert.alert('Hata', result.error);
              setPin('');
              setStep('enter');
              setConfirmPin('');
            }
          } else {
            Alert.alert(
              'PIN Eşleşmiyor',
              'Lütfen aynı PIN\'i tekrar girin',
              [{ text: 'Tamam' }]
            );
            setPin('');
            setStep('enter');
            setConfirmPin('');
          }
        }
      }
    } catch (error) {
      console.error('PIN işlemi hatası:', error);
      Alert.alert('Hata', 'Bir hata oluştu');
      setPin('');
    } finally {
      setIsLoading(false);
    }
  };

  // Başlık metni
  const getTitle = () => {
    if (mode === 'setup') {
      return step === 'enter' ? 'PIN Oluşturun' : 'PIN\'i Onaylayın';
    }
    return 'PIN Girin';
  };

  // Alt başlık metni
  const getSubtitle = () => {
    if (mode === 'setup') {
      return step === 'enter' ? '4 haneli PIN oluşturun' : 'PIN\'i tekrar girin';
    }
    return 'Uygulamaya erişmek için PIN\'inizi girin';
  };

  // Numara tuşları
  const renderNumberPad = () => {
    const numbers = [
      [1, 2, 3],
      [4, 5, 6],
      [7, 8, 9],
      ['biometric', 0, 'backspace']
    ];

    return (
      <View style={styles.numberPad}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((item, itemIndex) => {
              if (item === 'biometric') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[styles.numberButton, { backgroundColor: 'transparent' }]}
                    onPress={tryBiometricAuth}
                    disabled={!authSettings.biometricEnabled || mode !== 'auth'}
                  >
                    {authSettings.biometricEnabled && mode === 'auth' && (
                      <MaterialIcons 
                        name="fingerprint" 
                        size={24} 
                        color={theme.PRIMARY} 
                      />
                    )}
                  </TouchableOpacity>
                );
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[styles.numberButton, { backgroundColor: 'transparent' }]}
                    onPress={handleBackspace}
                    disabled={pin.length === 0}
                  >
                    <MaterialIcons 
                      name="backspace" 
                      size={24} 
                      color={pin.length > 0 ? theme.TEXT_PRIMARY : theme.TEXT_SECONDARY} 
                    />
                  </TouchableOpacity>
                );
              }

              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={[styles.numberButton, { backgroundColor: theme.CARD }]}
                  onPress={() => handleNumberPress(item.toString())}
                  disabled={isLoading}
                >
                  <Text style={[styles.numberText, { color: theme.TEXT_PRIMARY }]}>
                    {item}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  // PIN noktaları
  const renderPinDots = () => {
    return (
      <View style={styles.pinDots}>
        {[0, 1, 2, 3].map((index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              {
                backgroundColor: index < pin.length ? theme.PRIMARY : theme.BORDER,
                borderColor: theme.BORDER
              }
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={styles.header}>
        {mode !== 'auth' && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={24} color={theme.TEXT_PRIMARY} />
          </TouchableOpacity>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Logo/Icon */}
        <View style={styles.logoContainer}>
          <View style={[styles.logoCircle, { backgroundColor: theme.PRIMARY }]}>
            <MaterialIcons name="lock" size={32} color={theme.WHITE} />
          </View>
        </View>

        {/* Title */}
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          {getTitle()}
        </Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
          {getSubtitle()}
        </Text>

        {/* PIN Dots */}
        <Animated.View style={{ transform: [{ translateX: shakeAnimation }] }}>
          {renderPinDots()}
        </Animated.View>

        {/* Error Message */}
        {errorMessage && (
          <Text style={[styles.errorText, { color: Colors.DANGER }]}>
            {errorMessage}
          </Text>
        )}

        {/* Lockout Warning */}
        {isLockedOut && (
          <View style={styles.lockoutContainer}>
            <MaterialIcons name="lock-clock" size={24} color={Colors.DANGER} />
            <Text style={[styles.lockoutText, { color: Colors.DANGER }]}>
              Çok fazla hatalı deneme
            </Text>
            <Text style={[styles.lockoutTimer, { color: Colors.DANGER }]}>
              {lockoutTime} saniye sonra tekrar deneyin
            </Text>
          </View>
        )}

        {/* Number Pad */}
        {!isLockedOut && renderNumberPad()}

        {/* Remaining Attempts */}
        {remainingAttempts < 5 && mode === 'auth' && !isLockedOut && (
          <Text style={[styles.attemptsText, { color: Colors.WARNING }]}>
            Kalan deneme: {remainingAttempts}
          </Text>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.PRIMARY} />
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  logoContainer: {
    marginBottom: 32,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 48,
  },
  pinDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 48,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginHorizontal: 12,
    borderWidth: 2,
  },
  numberPad: {
    width: '100%',
    maxWidth: 300,
  },
  numberRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  numberButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  numberText: {
    fontSize: 24,
    fontWeight: '600',
  },
  attemptsText: {
    fontSize: 14,
    marginTop: 24,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    marginTop: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  lockoutContainer: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  lockoutText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  lockoutTimer: {
    fontSize: 18,
    fontWeight: '700',
    marginTop: 4,
    textAlign: 'center',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
});
