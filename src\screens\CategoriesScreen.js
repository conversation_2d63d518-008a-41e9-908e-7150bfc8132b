import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert } from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import CategoryForm from '../components/category/CategoryForm';

/**
 * Kategoriler ekranı
 *
 * @returns {JSX.Element} Kategoriler ekranı
 */
export default function CategoriesScreen() {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();

  const [categories, setCategories] = useState([]);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'income', 'expense', 'both'

  // Verileri yükle
  const loadData = async () => {
    try {
      setRefreshing(true);
      
      let query = `SELECT * FROM categories`;
      const params = [];
      
      if (filter !== 'all') {
        query += ` WHERE type = ?`;
        params.push(filter);
      }
      
      query += ` ORDER BY name ASC`;
      
      const result = await db.getAllAsync(query, params);
      setCategories(result);
    } catch (error) {
      console.error('Kategori yükleme hatası:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // İlk yükleme
  useEffect(() => {
    loadData();
  }, [filter]);

  // Kategori düzenleme
  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setIsAddingCategory(true);
  };

  // Kategori silme
  const handleDeleteCategory = async (category) => {
    // Varsayılan kategoriler silinemez
    if (category.is_default === 1) {
      Alert.alert(
        'Hata',
        'Varsayılan kategoriler silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }
    
    // Silme onayı
    Alert.alert(
      'Kategori Sil',
      `"${category.name}" kategorisini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              // Önce bu kategoriye ait işlemleri kontrol et
              const transactions = await db.getAllAsync(
                'SELECT COUNT(*) as count FROM transactions WHERE category_id = ?',
                [category.id]
              );
              
              const count = transactions[0]?.count || 0;
              
              if (count > 0) {
                // Kategori kullanılıyor, silme işlemi için onay al
                Alert.alert(
                  'Dikkat',
                  `Bu kategori ${count} işlemde kullanılıyor. Silmek istediğinize emin misiniz?`,
                  [
                    { text: 'İptal', style: 'cancel' },
                    {
                      text: 'Sil',
                      style: 'destructive',
                      onPress: async () => {
                        await deleteCategory(category.id);
                      }
                    }
                  ]
                );
              } else {
                // Kategori kullanılmıyor, doğrudan sil
                await deleteCategory(category.id);
              }
            } catch (error) {
              console.error('Kategori silme hatası:', error);
              Alert.alert('Hata', 'Kategori silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Kategori silme işlemi
  const deleteCategory = async (categoryId) => {
    try {
      await db.runAsync('DELETE FROM categories WHERE id = ?', [categoryId]);
      loadData();
    } catch (error) {
      console.error('Kategori silme hatası:', error);
      Alert.alert('Hata', 'Kategori silinirken bir hata oluştu.');
    }
  };

  // Kategori öğesi
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleEditCategory(item)}
    >
      <View style={[styles.categoryIcon, { backgroundColor: item.color || Colors.PRIMARY }]}>
        <MaterialIcons name={item.icon || 'category'} size={24} color="#fff" />
      </View>
      
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name}</Text>
        <Text style={styles.categoryType}>
          {item.type === 'income' ? 'Gelir' : item.type === 'expense' ? 'Gider' : 'Her İkisi'}
          {item.is_default === 1 ? ' (Varsayılan)' : ''}
        </Text>
      </View>
      
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteCategory(item)}
      >
        <MaterialIcons name="delete" size={24} color={Colors.DANGER} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  // Filtre butonları
  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      <TouchableOpacity
        style={[
          styles.filterButton,
          filter === 'all' && styles.activeFilterButton
        ]}
        onPress={() => setFilter('all')}
      >
        <Text style={[
          styles.filterButtonText,
          filter === 'all' && styles.activeFilterButtonText
        ]}>
          Tümü
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.filterButton,
          filter === 'income' && styles.activeIncomeButton
        ]}
        onPress={() => setFilter('income')}
      >
        <Text style={[
          styles.filterButtonText,
          filter === 'income' && styles.activeFilterButtonText
        ]}>
          Gelir
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.filterButton,
          filter === 'expense' && styles.activeExpenseButton
        ]}
        onPress={() => setFilter('expense')}
      >
        <Text style={[
          styles.filterButtonText,
          filter === 'expense' && styles.activeFilterButtonText
        ]}>
          Gider
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.filterButton,
          filter === 'both' && styles.activeBothButton
        ]}
        onPress={() => setFilter('both')}
      >
        <Text style={[
          styles.filterButtonText,
          filter === 'both' && styles.activeFilterButtonText
        ]}>
          Her İkisi
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Text style={styles.title}>Kategoriler</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            setEditingCategory(null);
            setIsAddingCategory(true);
          }}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {renderFilterButtons()}

      <FlatList
        data={categories}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderCategoryItem}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="category" size={64} color={'#999'} />
            <Text style={styles.emptyText}>Henüz kategori bulunmuyor</Text>
          </View>
        }
      />

      {isAddingCategory && (
        <CategoryForm
          visible={isAddingCategory}
          onClose={() => {
            setIsAddingCategory(false);
            setEditingCategory(null);
          }}
          onSave={() => {
            setIsAddingCategory(false);
            setEditingCategory(null);
            loadData();
          }}
          category={editingCategory}
          type={filter !== 'all' ? filter : 'expense'}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    marginHorizontal: 4,
    backgroundColor: '#fff',
  },
  activeFilterButton: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  activeIncomeButton: {
    backgroundColor: Colors.SUCCESS,
    borderColor: Colors.SUCCESS,
  },
  activeExpenseButton: {
    backgroundColor: Colors.DANGER,
    borderColor: Colors.DANGER,
  },
  activeBothButton: {
    backgroundColor: Colors.WARNING,
    borderColor: Colors.WARNING,
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#eee',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  categoryType: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
});
