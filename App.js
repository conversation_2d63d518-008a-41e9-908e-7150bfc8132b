import React, { Suspense, useState, useEffect, memo } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { MaterialIcons } from '@expo/vector-icons';
import { SafeAreaProvider, useSafeAreaInsets } from 'react-native-safe-area-context';
import { SQLiteProvider } from 'expo-sqlite';
import { initializeDatabase } from './src/services/DatabaseService';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ExchangeRateProvider } from './src/context/ExchangeRateProvider';
import { AppProvider } from './src/context/AppContext';
import { AuthProvider } from './src/context/AuthContext';
import AuthWrapper from './src/components/AuthWrapper';
import StatusBarManager from './src/components/StatusBarManager';
import * as notificationService from './src/services/NotificationService';
import * as notificationScheduler from './src/services/notificationScheduler';
import { initShiftBackgroundService } from './src/services/shiftBackgroundService';
import { runManualMigration } from './src/db/manualMigration';

// Performance utilities
import { performanceMonitor, memoryManager } from './src/utils/performanceUtils';

// Ekranlar
import HomeScreen from './src/screens/HomeScreen';
import TransactionsScreen from './src/screens/TransactionsScreen';
import FeaturesScreen from './src/screens/FeaturesScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import CategoriesScreen from './src/screens/CategoriesScreen';
import TransactionEditScreen from './src/screens/TransactionEditScreen';
import InvestmentScreen from './src/screens/InvestmentScreen';
import InvestmentAssetScreen from './src/screens/InvestmentAssetScreen';
import InvestmentAssetFormScreen from './src/screens/InvestmentAssetFormScreen';
import InvestmentTransactionsScreen from './src/screens/InvestmentTransactionsScreen';
import InvestmentTransactionFormScreen from './src/screens/InvestmentTransactionFormScreen';
import InvestmentTransactionDetailScreen from './src/screens/InvestmentTransactionDetailScreen';
import WorkScreen from './src/screens/WorkScreen';
import WorkPaymentsScreen from './src/screens/WorkPaymentsScreen';
import WorkPaymentDetailScreen from './src/screens/WorkPaymentDetailScreen';

// Yeni vardiya takibi modülü
import {
  ShiftHomeScreen,
  ShiftListScreen,
  ShiftDetailScreen,
  ShiftTypesScreen,
  ShiftScheduleScreen,
  ShiftSettingsScreen
} from './src/features/shifts';
import AppearanceSettingsScreen from './src/screens/AppearanceSettingsScreen';
import TabBarCustomizationScreen from './src/screens/TabBarCustomizationScreen';
import CurrencySettingsScreen from './src/screens/CurrencySettingsScreen';
import AuthScreen from './src/screens/AuthScreen';
import PinScreen from './src/screens/PinScreen';
import SavingsScreen from './src/screens/SavingsScreen';
import SavingsGoalFormScreen from './src/screens/SavingsGoalFormScreen';
import SavingsGoalDetail from './src/screens/SavingsGoalDetail';
import BudgetsScreen from './src/screens/BudgetsScreen';
import BudgetFormScreen from './src/screens/BudgetFormScreen';
import BudgetDetailScreen from './src/screens/BudgetDetailScreen';
import BudgetAlertFormScreen from './src/screens/BudgetAlertFormScreen';
import SalariesScreen from './src/screens/SalariesScreen';
// SalaryFormScreen import removed as part of migration to RegularIncomeForm
import SalaryDetailScreen from './src/screens/SalaryDetailScreen';
import CurrencyScreen from './src/screens/CurrencyScreen';
import TransactionDetailScreen from './src/screens/TransactionDetailScreen';
import OvertimeScreen from './src/screens/OvertimeScreen';
import OvertimeFormScreen from './src/screens/OvertimeFormScreen';
import OvertimeDetailScreen from './src/screens/OvertimeDetailScreen';
import NotificationsScreen from './src/screens/NotificationsScreen';
import NotificationSettingsScreen from './src/screens/NotificationSettingsScreen';
import RemindersScreen from './src/screens/RemindersScreen';
import ReminderFormScreen from './src/screens/ReminderFormScreen';
import ReminderDetailScreen from './src/screens/ReminderDetailScreen';
import ReminderGroupsScreen from './src/screens/ReminderGroupsScreen';
import RemindersByGroupScreen from './src/screens/RemindersByGroupScreen';
import ReminderStatsScreen from './src/screens/ReminderStatsScreen';
import ReminderTagsScreen from './src/screens/ReminderTagsScreen';
import ReminderTemplatesScreen from './src/screens/ReminderTemplatesScreen';
import ReminderTemplateFormScreen from './src/screens/ReminderTemplateFormScreen';
import ReminderPatternScreen from './src/screens/ReminderPatternScreen';
import ReminderPatternFormScreen from './src/screens/ReminderPatternFormScreen';
import ExpenseReminderFormScreen from './src/screens/ExpenseReminderFormScreen';
import SalaryReminderFormScreen from './src/screens/SalaryReminderFormScreen';
import SalaryPaymentForm from './src/screens/SalaryPaymentForm';
import RegularIncomeFormScreen from './src/screens/RegularIncomeFormScreen'; // Düzenli gelir formu ekranını import et
import ReportsScreen from './src/screens/ReportsScreen';
import ShoppingScreen from './src/screens/ShoppingScreen';
import CurrencyConverterScreen from './src/screens/CurrencyConverterScreen';
import StatisticsScreen from './src/screens/StatisticsScreen';
import SecuritySettingsScreen from './src/screens/SecuritySettingsScreen';
import PinAuthScreen from './src/screens/PinAuthScreen';
import PinResetScreen from './src/screens/PinResetScreen';
import AppTutorialScreen from './src/screens/AppTutorialScreen';
import AboutScreen from './src/screens/AboutScreen';
import ComingSoonScreen from './src/screens/ComingSoonScreen';

import { Colors } from './src/constants/colors';
import { useAppContext } from './src/context/AppContext';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();
const HomeStack = createStackNavigator();
const TransactionsStack = createStackNavigator();
const FeaturesStack = createStackNavigator();
const SettingsStack = createStackNavigator();
const InvestmentStack = createStackNavigator();
const WorkStack = createStackNavigator();

// Ana Sayfa Stack
function HomeStackScreen() {
  return (
    <HomeStack.Navigator>
      <HomeStack.Screen
        name="HomeMain"
        component={HomeScreen}
        options={{ headerShown: false }}
      />
      <HomeStack.Screen
        name="TransactionEdit"
        component={TransactionEditScreen}
        options={{
          title: 'İşlem Ekle/Düzenle',
          headerStyle: { backgroundColor: '#fff' },
          headerTintColor: '#333',
        }}
      />
    </HomeStack.Navigator>
  );
}

// İşlemler Stack
function TransactionsStackScreen() {
  return (
    <TransactionsStack.Navigator>
      <TransactionsStack.Screen
        name="TransactionsMain"
        component={TransactionsScreen}
        options={{ headerShown: false }}
      />
      <TransactionsStack.Screen
        name="TransactionEdit"
        component={TransactionEditScreen}
        options={{
          title: 'İşlem Ekle/Düzenle',
          headerStyle: { backgroundColor: '#fff' },
          headerTintColor: '#333',
        }}
      />
    </TransactionsStack.Navigator>
  );
}

// Özellikler Stack
function FeaturesStackScreen() {
  return (
    <FeaturesStack.Navigator>
      <FeaturesStack.Screen
        name="FeaturesMain"
        component={FeaturesScreen}
        options={{ headerShown: false }}
      />
      <FeaturesStack.Screen
        name="Investment"
        component={InvestmentStackScreen}
        options={{ headerShown: false }}
      />
      <FeaturesStack.Screen
        name="Work"
        component={WorkStackScreen}
        options={{ headerShown: false }}
      />
      <FeaturesStack.Screen
        name="Savings"
        component={SavingsScreen}
        options={{ title: 'Birikim Hedefleri' }}
      />
      <FeaturesStack.Screen
        name="SavingsGoalForm"
        component={SavingsGoalFormScreen}
        options={({ route }) => ({
          title: route.params?.goalId ? 'Hedefi Düzenle' : 'Yeni Hedef Ekle'
        })}
      />
      <FeaturesStack.Screen
        name="SavingsGoalDetail"
        component={SavingsGoalDetail}
        options={{ title: 'Hedef Detayı' }}
      />
      <FeaturesStack.Screen
        name="Budgets"
        component={BudgetsScreen}
        options={{ title: 'Bütçelerim', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="BudgetForm"
        component={BudgetFormScreen}
        options={({ route }) => ({
          title: route.params?.budget ? 'Bütçeyi Düzenle' : 'Yeni Bütçe Oluştur',
          headerShown: false
        })}
      />
      <FeaturesStack.Screen
        name="BudgetDetail"
        component={BudgetDetailScreen}
        options={{ title: 'Bütçe Detayı', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="BudgetAlertForm"
        component={BudgetAlertFormScreen}
        options={({ route }) => ({
          title: route.params?.alertId ? 'Bütçe Uyarısını Düzenle' : 'Yeni Bütçe Uyarısı',
          headerShown: false
        })}
      />
      <FeaturesStack.Screen
        name="Salaries"
        component={SalariesScreen}
        options={{ title: 'Maaşlarım' }}
      />
      {/* SalaryForm route removed as part of migration to RegularIncomeForm */}
      <FeaturesStack.Screen
        name="SalaryDetail"
        component={SalaryDetailScreen}
        options={{ title: 'Maaş Detayı' }}
      />
      <FeaturesStack.Screen
        name="SalaryPaymentForm"
        component={SalaryPaymentForm}
        options={({ route }) => ({
          title: route.params?.paymentId ? 'Ödemeyi Düzenle' : 'Yeni Ödeme'
        })}
      />
      <FeaturesStack.Screen
        name="RegularIncomeForm"
        component={RegularIncomeFormScreen}
        options={({ route }) => ({
          title: route.params?.incomeId ? 'Düzenli Geliri Düzenle' : 'Yeni Düzenli Gelir Ekle'
        })}
      />
      <FeaturesStack.Screen
        name="Currency"
        component={CurrencyScreen}
        options={{ title: 'Döviz Çevirici' }}
      />
      <FeaturesStack.Screen
        name="TransactionDetail"
        component={TransactionDetailScreen}
        options={{ title: 'İşlem Detayı' }}
      />
      <FeaturesStack.Screen
        name="Overtime"
        component={OvertimeScreen}
        options={{ title: 'Mesai Takibi' }}
      />
      <FeaturesStack.Screen
        name="OvertimeForm"
        component={OvertimeFormScreen}
        options={({ route }) => ({
          title: route.params?.overtimeId ? 'Mesai Düzenle' : 'Yeni Mesai Ekle'
        })}
      />
      <FeaturesStack.Screen
        name="OvertimeDetail"
        component={OvertimeDetailScreen}
        options={{ title: 'Mesai Detayı' }}
      />
      <FeaturesStack.Screen
        name="Reminders"
        component={RemindersScreen}
        options={{ title: 'Hatırlatıcılar' }}
      />
      <FeaturesStack.Screen
        name="ReminderForm"
        component={ReminderFormScreen}
        options={({ route }) => ({
          title: route.params?.reminderId ? 'Hatırlatıcı Düzenle' : 'Yeni Hatırlatıcı'
        })}
      />
      <FeaturesStack.Screen
        name="ExpenseReminderForm"
        component={ExpenseReminderFormScreen}
        options={({ route }) => ({
          title: route.params?.reminderId ? 'Harcama Hatırlatıcısını Düzenle' : 'Yeni Harcama Hatırlatıcısı'
        })}
      />
      <FeaturesStack.Screen
        name="SalaryReminderForm"
        component={SalaryReminderFormScreen}
        options={({ route }) => ({
          title: route.params?.reminderId ? 'Maaş Hatırlatıcısını Düzenle' : 'Yeni Maaş Hatırlatıcısı'
        })}
      />
      <FeaturesStack.Screen
        name="ReminderDetail"
        component={ReminderDetailScreen}
        options={{ title: 'Hatırlatıcı Detayı' }}
      />
      <FeaturesStack.Screen
        name="ReminderGroups"
        component={ReminderGroupsScreen}
        options={{ title: 'Hatırlatıcı Grupları' }}
      />
      <FeaturesStack.Screen
        name="RemindersByGroup"
        component={RemindersByGroupScreen}
        options={({ route }) => ({
          title: route.params?.groupName || 'Grup Hatırlatıcıları'
        })}
      />
      <FeaturesStack.Screen
        name="ReminderStats"
        component={ReminderStatsScreen}
        options={{ title: 'Hatırlatıcı İstatistikleri' }}
      />
      <FeaturesStack.Screen
        name="ReminderTags"
        component={ReminderTagsScreen}
        options={{ title: 'Hatırlatıcı Etiketleri' }}
      />
      <FeaturesStack.Screen
        name="ReminderTemplates"
        component={ReminderTemplatesScreen}
        options={{ title: 'Hatırlatıcı Şablonları' }}
      />
      <FeaturesStack.Screen
        name="ReminderTemplateForm"
        component={ReminderTemplateFormScreen}
        options={({ route }) => ({
          title: route.params?.templateId ? 'Şablonu Düzenle' : 'Yeni Şablon'
        })}
      />
      <FeaturesStack.Screen
        name="ReminderPatterns"
        component={ReminderPatternScreen}
        options={{ title: 'Özel Tekrarlama Desenleri' }}
      />
      <FeaturesStack.Screen
        name="ReminderPatternForm"
        component={ReminderPatternFormScreen}
        options={({ route }) => ({
          title: route.params?.patternId ? 'Deseni Düzenle' : 'Yeni Desen'
        })}
      />
      <FeaturesStack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{ title: 'Bildirimler' }}
      />
      <FeaturesStack.Screen
        name="NotificationSettings"
        component={NotificationSettingsScreen}
        options={{ title: 'Bildirim Ayarları' }}
      />
      <FeaturesStack.Screen
        name="Reports"
        component={ReportsScreen}
        options={{ title: 'Raporlar', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="Shopping"
        component={ShoppingScreen}
        options={{ title: 'Alışveriş Listesi', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="CurrencyConverter"
        component={CurrencyConverterScreen}
        options={{ title: 'Döviz Çevirici', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="Statistics"
        component={StatisticsScreen}
        options={{ title: 'İstatistikler', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="Transactions"
        component={TransactionsScreen}
        options={{ title: 'İşlemler', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{ title: 'Kategori Yönetimi', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="SecuritySettings"
        component={SecuritySettingsScreen}
        options={{ title: 'Güvenlik Ayarları', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="TabBarCustomization"
        component={TabBarCustomizationScreen}
        options={{ title: 'Tab Bar Özelleştirme', headerShown: false }}
      />
      <FeaturesStack.Screen
        name="ComingSoon"
        component={ComingSoonScreen}
        options={{ title: 'Özellik Durumu', headerShown: false }}
      />







    </FeaturesStack.Navigator>
  );
}

// Ayarlar Stack
function SettingsStackScreen() {
  return (
    <SettingsStack.Navigator>
      <SettingsStack.Screen
        name="SettingsMain"
        component={SettingsScreen}
        options={{ headerShown: false }}
      />
      <SettingsStack.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{ title: 'Kategoriler' }}
      />
      <SettingsStack.Screen
        name="AppearanceSettings"
        component={AppearanceSettingsScreen}
        options={{ title: 'Görünüm Ayarları' }}
      />
      <SettingsStack.Screen
        name="TabBarCustomization"
        component={TabBarCustomizationScreen}
        options={{ title: 'Tab Bar Özelleştirme' }}
      />
      <SettingsStack.Screen
        name="CurrencySettings"
        component={CurrencySettingsScreen}
        options={{ title: 'Para Birimi Ayarları' }}
      />
      <SettingsStack.Screen
        name="SecuritySettings"
        component={SecuritySettingsScreen}
        options={{ title: 'Güvenlik Ayarları', headerShown: false }}
      />
      <SettingsStack.Screen
        name="PinAuth"
        component={PinAuthScreen}
        options={{ title: 'PIN Ayarları', headerShown: false }}
      />
      <SettingsStack.Screen
        name="About"
        component={AboutScreen}
        options={{ title: 'Hakkında', headerShown: false }}
      />
      <SettingsStack.Screen
        name="PinReset"
        component={PinResetScreen}
        options={{ title: 'PIN Sıfırla', headerShown: false }}
      />
      <SettingsStack.Screen
        name="AppTutorial"
        component={AppTutorialScreen}
        options={{ title: 'Kullanım Rehberi', headerShown: false }}
      />
    </SettingsStack.Navigator>
  );
}

// Yatırım Stack
function InvestmentStackScreen() {
  return (
    <InvestmentStack.Navigator>
      <InvestmentStack.Screen
        name="InvestmentMain"
        component={InvestmentScreen}
        options={{ title: 'Yatırımlarım' }}
      />
      <InvestmentStack.Screen
        name="InvestmentAsset"
        component={InvestmentAssetScreen}
        options={({ route }) => ({
          title: route.params?.asset?.name || 'Varlık Detayı'
        })}
      />
      <InvestmentStack.Screen
        name="InvestmentAssetForm"
        component={InvestmentAssetFormScreen}
        options={({ route }) => ({
          title: route.params?.asset ? 'Varlık Düzenle' : 'Yeni Varlık Ekle'
        })}
      />
      <InvestmentStack.Screen
        name="InvestmentTransactions"
        component={InvestmentTransactionsScreen}
        options={{ title: 'Yatırım İşlemleri' }}
      />
      <InvestmentStack.Screen
        name="InvestmentTransactionForm"
        component={InvestmentTransactionFormScreen}
        options={({ route }) => ({
          title: route.params?.transaction ? 'İşlem Düzenle' : 'Yeni İşlem Ekle'
        })}
      />
      <InvestmentStack.Screen
        name="InvestmentTransactionDetail"
        component={InvestmentTransactionDetailScreen}
        options={{ title: 'İşlem Detayı' }}
      />
    </InvestmentStack.Navigator>
  );
}

// Mesai ve Vardiya Stack
function WorkStackScreen() {
  return (
    <WorkStack.Navigator>
      <WorkStack.Screen
        name="WorkMain"
        component={WorkScreen}
        options={{ title: 'Vardiya Takibi', headerShown: false }}
      />
      <WorkStack.Screen
        name="ShiftList"
        component={ShiftListScreen}
        options={{ headerShown: false }}
      />
      <WorkStack.Screen
        name="ShiftDetail"
        component={ShiftDetailScreen}
        options={{ headerShown: false }}
      />
      <WorkStack.Screen
        name="ShiftTypes"
        component={ShiftTypesScreen}
        options={{ headerShown: false }}
      />
      <WorkStack.Screen
        name="ShiftSchedule"
        component={ShiftScheduleScreen}
        options={{ headerShown: false }}
      />
      <WorkStack.Screen
        name="ShiftSettings"
        component={ShiftSettingsScreen}
        options={{ headerShown: false }}
      />
      <WorkStack.Screen
        name="WorkPayments"
        component={WorkPaymentsScreen}
        options={{ title: 'Ödemeler' }}
      />
      <WorkStack.Screen
        name="WorkPaymentDetail"
        component={WorkPaymentDetailScreen}
        options={{ title: 'Ödeme Detayı' }}
      />
    </WorkStack.Navigator>
  );
}

// Ana Tab Navigator
function TabNavigator() {
  const { theme } = useAppContext();
  const insets = useSafeAreaInsets();

  // Tab bar ayarları
  const [tabBarSettings, setTabBarSettings] = useState([
    { id: 'home', title: 'Ana Sayfa', icon: 'home', component: 'HomeScreen' },
    { id: 'transactions', title: 'İşlemler', icon: 'receipt-long', component: 'TransactionsScreen' },
    { id: 'features', title: 'Özellikler', icon: 'apps', component: 'FeaturesScreen' },
    { id: 'settings', title: 'Ayarlar', icon: 'settings', component: 'SettingsScreen' }
  ]);

  // Tab bar ayarlarını yükle
  useEffect(() => {
    loadTabBarSettings();
  }, []);

  const loadTabBarSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('tabBarSettings');
      if (savedSettings) {
        setTabBarSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Tab bar ayarları yükleme hatası:', error);
    }
  };

  // Component mapping
  const getComponentForTab = (tabId) => {
    const componentMap = {
      'home': HomeStackScreen,
      'transactions': TransactionsStackScreen,
      'features': FeaturesStackScreen,
      'work': WorkStackScreen,
      'settings': SettingsStackScreen,
      'investment': InvestmentStackScreen,
      'stats': FeaturesStackScreen, // Geçici olarak Features'a yönlendir
    };
    return componentMap[tabId] || FeaturesStackScreen;
  };

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size }) => {
          // Dinamik icon mapping
          const currentTab = tabBarSettings.find(tab =>
            tab.title === route.name ||
            tab.id === route.name.toLowerCase()
          );
          const iconName = currentTab?.icon || 'apps';

          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.PRIMARY,
        tabBarInactiveTintColor: theme.TEXT_SECONDARY,
        tabBarStyle: {
          height: 60 + insets.bottom,
          paddingBottom: insets.bottom + 5,
          paddingTop: 5,
          backgroundColor: theme.CARD,
          borderTopColor: theme.BORDER,
        },
      })}
    >
      {tabBarSettings.map((tab) => (
        <Tab.Screen
          key={tab.id}
          name={tab.title}
          component={getComponentForTab(tab.id)}
          options={{
            title: tab.title,
            headerShown: false
          }}
        />
      ))}
    </Tab.Navigator>
  );
}

// Ana Stack Navigator
function MainStackNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="Auth"
        component={AuthScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Pin"
        component={PinScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Main"
        component={TabNavigator}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
}

// Performance optimized Loading Screen
const LoadingScreen = memo(() => {
  performanceMonitor.start('app_loading');

  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#3498db" />
      <Text style={styles.loadingText}>Yükleniyor...</Text>
    </View>
  );
});

LoadingScreen.displayName = 'LoadingScreen';

export default function App() {
  // Bildirim ve arka plan servisleri
  React.useEffect(() => {
    // Vardiya arka plan servisini başlat
    initShiftBackgroundService().catch(error => {
      console.warn('Vardiya arka plan servisi başlatılamadı:', error);
    });

    // Not: Expo Go'da push notification desteği sınırlıdır
    // Development build kullanmanız önerilir
  }, []);

  // Veritabanı başlatıldığında migrasyon işlemlerini çalıştır
  const onDatabaseInit = async (db) => {
    performanceMonitor.start('database_init');

    try {
      // fixDatabase çağrısı kaldırıldı - migration'lar bu işi yapıyor

      // Manuel migrasyon çalıştır
      await runManualMigration(db);

      // Normal veritabanı başlatma işlemini yap
      const result = await initializeDatabase(db);

      // Memory cleanup
      memoryManager.forceGC();

      performanceMonitor.end('database_init');
      // Database initialization completed successfully

      return result;
    } catch (error) {
      performanceMonitor.end('database_init');
      console.error('Veritabanı başlatma hatası:', error);

      // Hata durumunda da veritabanı bağlantısını döndür
      return db;
    }
  };

  return (
    <SafeAreaProvider>
      <Suspense fallback={<LoadingScreen />}>
        <SQLiteProvider databaseName="financial_app.db" onInit={onDatabaseInit} useSuspense>
          <ExchangeRateProvider>
            <AppProvider>
              <AuthProvider>
                <AuthWrapper>
                  <NavigationContainer>
                    <MainStackNavigator />
                  </NavigationContainer>
                  <StatusBarManager />
                </AuthWrapper>
              </AuthProvider>
            </AppProvider>
          </ExchangeRateProvider>
        </SQLiteProvider>
      </Suspense>
    </SafeAreaProvider>
  );
}

// Performance optimized styles
const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
});
