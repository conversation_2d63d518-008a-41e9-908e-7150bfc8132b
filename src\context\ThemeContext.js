import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from 'react-native';
import { Colors } from '../constants/colors'; // theme değil, themes

// Tema context'i oluştur - varsayılan değerle başlat
const ThemeContext = createContext({
  theme: COLORS,
  isDarkMode: false,
  themeType: 'light',
  isSystemTheme: true,
  changeTheme: () => {},
  toggleUseSystemTheme: () => {}
});

/**
 * Tema sağlayıcı bileşeni
 * @param {Object} props - Bileşen props'ları
 * @returns {JSX.Element} ThemeProvider bileşeni
 */
export const ThemeProvider = ({ children }) => {
  // Sistem teması tercihi
  const systemColorScheme = useColorScheme();
  
  // Tema ve ayarlar state'i
  const [theme, setTheme] = useState('light');
  const [isSystemTheme, setIsSystemTheme] = useState(true);
  
  // Mevcut tema - varsayılan light tema kullan
  const themeType = isSystemTheme 
    ? (systemColorScheme || 'light') 
    : (theme || 'light');
    
  const isDarkMode = themeType === 'dark';

  // Tema ayarlarını yükle
  useEffect(() => {
    const loadThemeSettings = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('userTheme');
        const savedIsSystemTheme = await AsyncStorage.getItem('useSystemTheme');
        
        if (savedTheme) {
          setTheme(savedTheme);
        }
        
        if (savedIsSystemTheme !== null) {
          setIsSystemTheme(savedIsSystemTheme === 'true');
        }
      } catch (error) {
        console.error('Tema ayarları yüklenirken hata:', error);
      }
    };
    
    loadThemeSettings();
  }, []);

  /**
   * Temayı değiştirme işlevi
   * @param {string} newTheme - 'dark' veya 'light'
   */
  const changeTheme = async (newTheme) => {
    setTheme(newTheme);
    setIsSystemTheme(false);
    try {
      await AsyncStorage.setItem('userTheme', newTheme);
      await AsyncStorage.setItem('useSystemTheme', 'false');
    } catch (error) {
      console.error('Tema ayarları kaydedilirken hata:', error);
    }
  };

  /**
   * Sistem temasını kullanmayı aç/kapa
   * @param {boolean} useSystem - Sistem teması kullanılsın mı
   */
  const toggleUseSystemTheme = async (useSystem) => {
    setIsSystemTheme(useSystem);
    try {
      await AsyncStorage.setItem('useSystemTheme', String(useSystem));
    } catch (error) {
      console.error('Tema ayarları kaydedilirken hata:', error);
    }
  };

  // Context değeri
  const value = {
    theme: COLORS, // Doğrudan COLORS kullan
    isDarkMode,
    themeType,
    isSystemTheme,
    changeTheme,
    toggleUseSystemTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Tema context'ini kullanmak için hook
 * @returns {Object} Tema context değerleri
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
