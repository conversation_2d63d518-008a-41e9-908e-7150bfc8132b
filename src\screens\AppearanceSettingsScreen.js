import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  useWindowDimensions,
  Alert
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Colors } from '../constants/colors';

/**
 * Tema ayarları ekranı
 * @returns {JSX.Element} AppearanceSettingsScreen bileşeni
 */
export default function AppearanceSettingsScreen() {
  const { width } = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const [themeType, setThemeType] = useState('light');
  const [isSystemTheme, setIsSystemTheme] = useState(false);

  // Tema değiştirme işlevi
  const handleChangeTheme = (newTheme) => {
    if (newTheme === themeType) return;

    setThemeType(newTheme);
    Alert.alert(
      '<PERSON><PERSON>',
      `Tema ${newTheme === 'dark' ? 'karanlık' : 'açık'} moda ayarlandı.`,
      [{ text: 'Tamam' }]
    );
  };

  // Sistem teması kullanımı değiştirme işlevi
  const handleToggleSystemTheme = (value) => {
    setIsSystemTheme(value);

    if (value) {
      Alert.alert(
        'Sistem Teması',
        'Uygulama, cihazınızın tema ayarlarını kullanacak.',
        [{ text: 'Tamam' }]
      );
    }
  };

  return (
    <ScrollView
      style={[
        styles.container,
        {
          backgroundColor: '#f5f5f5',
          paddingBottom: insets.bottom
        }
      ]}
    >
      <View style={styles.header}>
        <Text style={[styles.title, { color: '#333' }]}>
          Görünüm Ayarları
        </Text>
        <Text style={[styles.subtitle, { color: '#666' }]}>
          Uygulamanın görünümünü özelleştirin
        </Text>
      </View>

      {/* Sistem Teması Kullanımı */}
      <View style={[styles.section, { backgroundColor: '#fff' }]}>
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={[styles.settingTitle, { color: '#333' }]}>
              Sistem Temasını Kullan
            </Text>
            <Text style={[styles.settingDescription, { color: '#666' }]}>
              Cihaz ayarlarınıza göre otomatik olarak açık veya karanlık tema kullanılır
            </Text>
          </View>
          <Switch
            value={isSystemTheme}
            onValueChange={handleToggleSystemTheme}
            trackColor={{
              false: '#ccc',
              true: Colors.PRIMARY_LIGHT
            }}
            thumbColor={isSystemTheme ? Colors.PRIMARY : '#f5f5f5'}
          />
        </View>
      </View>

      {/* Manuel Tema Seçimi */}
      {!isSystemTheme && (
        <View style={[styles.section, { backgroundColor: '#fff' }]}>
          <Text style={[styles.sectionTitle, { color: '#333' }]}>
            Tema Seçin
          </Text>

          <View style={styles.themeOptions}>
            {/* Açık Tema */}
            <TouchableOpacity
              style={[
                styles.themeOption,
                themeType === 'light' && styles.selectedThemeOption,
                {
                  borderColor: themeType === 'light' ? Colors.PRIMARY : '#ddd',
                  width: (width - 60) / 2
                }
              ]}
              onPress={() => handleChangeTheme('light')}
            >
              <View style={[styles.themePreview, styles.lightThemePreview]}>
                <View style={styles.themePreviewHeader} />
                <View style={styles.themePreviewContent}>
                  <View style={styles.themePreviewLine} />
                  <View style={styles.themePreviewLine} />
                </View>
              </View>
              <Text style={[styles.themeOptionText, { color: '#333' }]}>
                Açık Tema
              </Text>
              {themeType === 'light' && (
                <MaterialIcons name="check-circle" size={20} color={Colors.PRIMARY} />
              )}
            </TouchableOpacity>

            {/* Karanlık Tema */}
            <TouchableOpacity
              style={[
                styles.themeOption,
                themeType === 'dark' && styles.selectedThemeOption,
                {
                  borderColor: themeType === 'dark' ? Colors.PRIMARY : '#ddd',
                  width: (width - 60) / 2
                }
              ]}
              onPress={() => handleChangeTheme('dark')}
            >
              <View style={[styles.themePreview, styles.darkThemePreview]}>
                <View style={[styles.themePreviewHeader, styles.darkThemePreviewHeader]} />
                <View style={[styles.themePreviewContent, styles.darkThemePreviewContent]}>
                  <View style={[styles.themePreviewLine, styles.darkThemePreviewLine]} />
                  <View style={[styles.themePreviewLine, styles.darkThemePreviewLine]} />
                </View>
              </View>
              <Text style={[styles.themeOptionText, { color: '#333' }]}>
                Karanlık Tema
              </Text>
              {themeType === 'dark' && (
                <MaterialIcons name="check-circle" size={20} color={Colors.PRIMARY} />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.noteContainer}>
            <MaterialIcons name="info" size={18} color="#2196F3" />
            <Text style={[styles.noteText, { color: '#666' }]}>
              Karanlık tema, pil tasarrufu sağlar ve göz yorgunluğunu azaltabilir.
            </Text>
          </View>
        </View>
      )}

      {/* Gelecek Tema Özellikleri Bilgisi */}
      <View style={[styles.infoBox, { backgroundColor: '#e3f2fd' }]}>
        <MaterialIcons name="lightbulb" size={24} color="#2196F3" />
        <View style={styles.infoContent}>
          <Text style={[styles.infoTitle, { color: '#333' }]}>
            Tema Özellikleri
          </Text>
          <Text style={[styles.infoText, { color: '#666' }]}>
            Çok yakında özel renk temaları ve daha fazla kişiselleştirme seçeneği eklenecektir.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 40
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16
  },
  section: {
    margin: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  settingInfo: {
    flex: 1,
    paddingRight: 16
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4
  },
  settingDescription: {
    fontSize: 14
  },
  themeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    marginBottom: 20
  },
  themeOption: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1
  },
  selectedThemeOption: {
    borderWidth: 2
  },
  themePreview: {
    width: '100%',
    height: 100,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 12
  },
  lightThemePreview: {
    backgroundColor: '#ffffff'
  },
  darkThemePreview: {
    backgroundColor: '#121212'
  },
  themePreviewHeader: {
    height: 20,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef'
  },
  darkThemePreviewHeader: {
    backgroundColor: '#1e1e1e',
    borderBottomColor: '#333333'
  },
  themePreviewContent: {
    padding: 10
  },
  darkThemePreviewContent: {
    backgroundColor: '#121212'
  },
  themePreviewLine: {
    height: 10,
    backgroundColor: '#e9ecef',
    marginBottom: 8,
    borderRadius: 2
  },
  darkThemePreviewLine: {
    backgroundColor: '#333333'
  },
  themeOptionText: {
    fontSize: 16,
    fontWeight: '500',
    marginVertical: 4
  },
  noteContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8
  },
  noteText: {
    marginLeft: 8,
    flex: 1,
    fontSize: 14,
    lineHeight: 20
  },
  infoBox: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  infoContent: {
    marginLeft: 12,
    flex: 1
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20
  }
});
