import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  FlatList,
  Image
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { useExchangeRate } from '../context/ExchangeRateProvider';
import * as investmentService from '../services/investmentService';
import { formatCurrency } from '../utils/formatters';

/**
 * Yatırım ekranı
 *
 * @returns {JSX.Element} Yatırım ekranı
 */
export default function InvestmentScreen() {
  const navigation = useNavigation();
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  const exchangeRateContext = useExchangeRate();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [portfolioSummary, setPortfolioSummary] = useState(null);
  const [portfolio, setPortfolio] = useState([]);
  const [assetTypes, setAssetTypes] = useState([]);
  const [selectedType, setSelectedType] = useState('all');
  const [recentTransactions, setRecentTransactions] = useState([]);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Portföy özetini getir
      const summary = await investmentService.getPortfolioSummary();
      setPortfolioSummary(summary);

      // Portföyü getir
      const portfolioData = await investmentService.getPortfolio();
      setPortfolio(portfolioData);

      // Varlık tiplerini getir
      const types = await db.getAllAsync(`
        SELECT DISTINCT type FROM investment_assets
        ORDER BY type
      `);
      setAssetTypes(types.map(t => t.type));

      // Son işlemleri getir
      const transactions = await investmentService.getInvestmentTransactions({
        limit: 5
      });
      setRecentTransactions(transactions);

      setLoading(false);
    } catch (error) {
      console.error('Yatırım verileri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yenile
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  // Yeni yatırım işlemi ekle
  const addNewTransaction = () => {
    navigation.navigate('InvestmentTransactionForm');
  };

  // Yeni yatırım varlığı ekle
  const addNewAsset = () => {
    navigation.navigate('InvestmentAssetForm');
  };

  // Varlık detaylarını görüntüle
  const viewAssetDetails = (assetId) => {
    navigation.navigate('InvestmentAsset', { assetId });
  };

  // İşlem detaylarını görüntüle
  const viewTransactionDetails = (transactionId) => {
    navigation.navigate('InvestmentTransactionDetail', { transactionId });
  };

  // Varlık tipine göre filtrele
  const filterByType = (type) => {
    setSelectedType(type);
  };

  // Filtrelenmiş portföy
  const filteredPortfolio = selectedType === 'all'
    ? portfolio
    : portfolio.filter(item => item.asset_type === selectedType);

  // Varlık tipi adını formatla
  const formatAssetType = (type) => {
    switch (type) {
      case 'stock': return 'Hisse Senedi';
      case 'crypto': return 'Kripto Para';
      case 'gold': return 'Altın';
      case 'silver': return 'Gümüş';
      case 'forex': return 'Döviz';
      case 'bond': return 'Tahvil/Bono';
      case 'fund': return 'Fon';
      case 'other': return 'Diğer';
      default: return type;
    }
  };

  // İşlem tipini formatla
  const formatTransactionType = (type) => {
    switch (type) {
      case 'buy': return 'Alım';
      case 'sell': return 'Satım';
      case 'dividend': return 'Temettü';
      case 'interest': return 'Faiz';
      case 'fee': return 'Komisyon';
      case 'transfer': return 'Transfer';
      case 'other': return 'Diğer';
      default: return type;
    }
  };

  // İşlem tipine göre renk
  const getTransactionTypeColor = (type) => {
    switch (type) {
      case 'buy': return Colors.expense.main;
      case 'sell': return Colors.income.main;
      case 'dividend': return Colors.income.main;
      case 'interest': return Colors.income.main;
      case 'fee': return Colors.DANGER;
      case 'transfer': return Colors.INFO;
      case 'other': return Colors.GRAY_600;
      default: return Colors.GRAY_600;
    }
  };

  // Varlık tipine göre ikon
  const getAssetTypeIcon = (type) => {
    switch (type) {
      case 'stock': return 'show-chart';
      case 'crypto': return 'currency-bitcoin';
      case 'gold': return 'monetization-on';
      case 'silver': return 'monetization-on';
      case 'forex': return 'currency-exchange';
      case 'bond': return 'receipt-long';
      case 'fund': return 'account-balance';
      case 'other': return 'category';
      default: return 'category';
    }
  };

  // Kar/zarar durumuna göre renk
  const getProfitLossColor = (value) => {
    if (value > 0) return Colors.income.main;
    if (value < 0) return Colors.expense.main;
    return Colors.GRAY_600;
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>Yatırım verileri yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.CARD, borderBottomColor: theme.BORDER }]}>
        <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>Yatırımlarım</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={addNewAsset}
          >
            <MaterialIcons name="add-circle-outline" size={24} color={theme.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={addNewTransaction}
          >
            <MaterialIcons name="add" size={24} color={theme.PRIMARY} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.PRIMARY]}
            tintColor={Colors.PRIMARY}
          />
        }
      >
        {/* Portföy Özeti */}
        <View style={[styles.summaryCard, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
          <Text style={[styles.summaryTitle, { color: theme.TEXT_PRIMARY }]}>Portföy Özeti</Text>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Değer</Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(portfolioSummary?.totalValue || 0, 'TRY')}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Kar/Zarar</Text>
            <Text style={[
              styles.summaryValue,
              { color: getProfitLossColor(portfolioSummary?.totalProfitLoss || 0) }
            ]}>
              {formatCurrency(portfolioSummary?.totalProfitLoss || 0, 'TRY')}
              {' '}
              ({(portfolioSummary?.profitLossPercentage || 0).toFixed(2)}%)
            </Text>
          </View>

          {/* Dağılım */}
          {portfolioSummary?.distribution && portfolioSummary.distribution.length > 0 && (
            <View style={styles.distributionContainer}>
              <Text style={styles.distributionTitle}>Dağılım</Text>
              <View style={styles.distributionChart}>
                {portfolioSummary.distribution.map((item, index) => {
                  const percentage = (item.value / portfolioSummary.totalValue) * 100;
                  return (
                    <View key={index} style={styles.distributionItem}>
                      <View style={styles.distributionLabelContainer}>
                        <MaterialIcons
                          name={getAssetTypeIcon(item.type)}
                          size={16}
                          color={Colors.GRAY_700}
                        />
                        <Text style={styles.distributionLabel}>
                          {formatAssetType(item.type)}
                        </Text>
                      </View>
                      <View style={styles.distributionBarContainer}>
                        <View
                          style={[
                            styles.distributionBar,
                            {
                              width: `${percentage}%`,
                              backgroundColor: getAssetTypeColor(item.type)
                            }
                          ]}
                        />
                      </View>
                      <Text style={styles.distributionPercentage}>
                        {percentage.toFixed(1)}%
                      </Text>
                    </View>
                  );
                })}
              </View>
            </View>
          )}
        </View>

        {/* Varlık Tipleri */}
        <View style={styles.assetTypesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.assetTypesContent}
          >
            <TouchableOpacity
              style={[
                styles.assetTypeButton,
                selectedType === 'all' && styles.assetTypeButtonSelected
              ]}
              onPress={() => filterByType('all')}
            >
              <Text style={[
                styles.assetTypeButtonText,
                selectedType === 'all' && styles.assetTypeButtonTextSelected
              ]}>
                Tümü
              </Text>
            </TouchableOpacity>

            {assetTypes.map((type, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.assetTypeButton,
                  selectedType === type && styles.assetTypeButtonSelected
                ]}
                onPress={() => filterByType(type)}
              >
                <MaterialIcons
                  name={getAssetTypeIcon(type)}
                  size={16}
                  color={selectedType === type ? Colors.WHITE : Colors.GRAY_700}
                />
                <Text style={[
                  styles.assetTypeButtonText,
                  selectedType === type && styles.assetTypeButtonTextSelected
                ]}>
                  {formatAssetType(type)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Portföy */}
        <View style={styles.portfolioContainer}>
          <Text style={styles.sectionTitle}>Portföyüm</Text>

          {filteredPortfolio.length === 0 ? (
            <View style={styles.emptyState}>
              <MaterialIcons name="account-balance-wallet" size={48} color={Colors.GRAY_400} />
              <Text style={styles.emptyStateText}>Henüz yatırım varlığınız bulunmuyor.</Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={addNewAsset}
              >
                <Text style={styles.emptyStateButtonText}>Yatırım Varlığı Ekle</Text>
              </TouchableOpacity>
            </View>
          ) : (
            filteredPortfolio.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={styles.portfolioItem}
                onPress={() => viewAssetDetails(item.asset_id)}
              >
                <View style={styles.portfolioItemHeader}>
                  <View style={styles.portfolioItemInfo}>
                    <MaterialIcons
                      name={getAssetTypeIcon(item.asset_type)}
                      size={20}
                      color={item.color || Colors.GRAY_700}
                    />
                    <View style={styles.portfolioItemDetails}>
                      <Text style={styles.portfolioItemName}>{item.asset_name}</Text>
                      <Text style={styles.portfolioItemSymbol}>{item.asset_symbol}</Text>
                    </View>
                  </View>
                  <View style={styles.portfolioItemValues}>
                    <Text style={styles.portfolioItemValue}>
                      {formatCurrency(item.current_value, 'TRY')}
                    </Text>
                    <Text style={[
                      styles.portfolioItemProfitLoss,
                      { color: getProfitLossColor(item.profit_loss) }
                    ]}>
                      {item.profit_loss > 0 ? '+' : ''}
                      {formatCurrency(item.profit_loss, 'TRY')}
                      {' '}
                      ({item.profit_loss_percentage > 0 ? '+' : ''}
                      {item.profit_loss_percentage.toFixed(2)}%)
                    </Text>
                  </View>
                </View>

                <View style={styles.portfolioItemFooter}>
                  <View style={styles.portfolioItemQuantity}>
                    <Text style={styles.portfolioItemQuantityLabel}>Miktar:</Text>
                    <Text style={styles.portfolioItemQuantityValue}>
                      {item.quantity.toFixed(item.asset_type === 'crypto' ? 8 : 2)}
                    </Text>
                  </View>
                  <View style={styles.portfolioItemPrice}>
                    <Text style={styles.portfolioItemPriceLabel}>Güncel Fiyat:</Text>
                    <Text style={styles.portfolioItemPriceValue}>
                      {formatCurrency(item.current_price, 'TRY')}
                    </Text>
                  </View>
                  <View style={styles.portfolioItemAvgPrice}>
                    <Text style={styles.portfolioItemAvgPriceLabel}>Ort. Alış:</Text>
                    <Text style={styles.portfolioItemAvgPriceValue}>
                      {formatCurrency(item.average_buy_price, 'TRY')}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>

        {/* Son İşlemler */}
        <View style={styles.recentTransactionsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Son İşlemler</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('InvestmentTransactions')}
            >
              <Text style={styles.seeAllButton}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>

          {recentTransactions.length === 0 ? (
            <View style={styles.emptyState}>
              <MaterialIcons name="receipt-long" size={48} color={Colors.GRAY_400} />
              <Text style={styles.emptyStateText}>Henüz işlem bulunmuyor.</Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={addNewTransaction}
              >
                <Text style={styles.emptyStateButtonText}>İşlem Ekle</Text>
              </TouchableOpacity>
            </View>
          ) : (
            recentTransactions.map((transaction, index) => (
              <TouchableOpacity
                key={index}
                style={styles.transactionItem}
                onPress={() => viewTransactionDetails(transaction.id)}
              >
                <View style={styles.transactionItemLeft}>
                  <View style={[
                    styles.transactionTypeIndicator,
                    { backgroundColor: getTransactionTypeColor(transaction.type) }
                  ]}>
                    <MaterialIcons
                      name={transaction.type === 'buy' ? 'arrow-downward' : 'arrow-upward'}
                      size={16}
                      color="#fff"
                    />
                  </View>
                  <View style={styles.transactionItemDetails}>
                    <Text style={styles.transactionItemAsset}>
                      {transaction.asset_name} ({transaction.asset_symbol})
                    </Text>
                    <Text style={styles.transactionItemType}>
                      {formatTransactionType(transaction.type)}
                    </Text>
                  </View>
                </View>
                <View style={styles.transactionItemRight}>
                  <Text style={styles.transactionItemAmount}>
                    {formatCurrency(transaction.total_amount, transaction.currency || 'TRY')}
                  </Text>
                  <Text style={styles.transactionItemDate}>
                    {new Date(transaction.date).toLocaleDateString('tr-TR')}
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>

      {/* Hızlı İşlem Ekleme Butonu */}
      <TouchableOpacity
        style={styles.fab}
        onPress={addNewTransaction}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

// Varlık tipine göre renk
const getAssetTypeColor = (type) => {
  switch (type) {
    case 'stock': return Colors.PRIMARY;
    case 'crypto': return Colors.SECONDARY;
    case 'gold': return '#fdcb6e';
    case 'silver': return '#b2bec3';
    case 'forex': return Colors.INFO;
    case 'bond': return Colors.WARNING;
    case 'fund': return Colors.SUCCESS;
    case 'other': return Colors.GRAY_600;
    default: return Colors.GRAY_600;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_100,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_700,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  summaryCard: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    margin: 16,
    padding: 16,
    shadowColor: Colors.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.GRAY_700,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
  },
  distributionContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
  },
  distributionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
    marginBottom: 12,
  },
  distributionChart: {
    marginTop: 8,
  },
  distributionItem: {
    marginBottom: 12,
  },
  distributionLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  distributionLabel: {
    fontSize: 14,
    color: Colors.GRAY_700,
    marginLeft: 4,
  },
  distributionBarContainer: {
    height: 8,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 2,
  },
  distributionBar: {
    height: '100%',
    borderRadius: 4,
  },
  distributionPercentage: {
    fontSize: 12,
    color: Colors.GRAY_600,
    textAlign: 'right',
  },
  assetTypesContainer: {
    marginBottom: 16,
  },
  assetTypesContent: {
    paddingHorizontal: 16,
  },
  assetTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: Colors.WHITE,
    borderRadius: 20,
    marginRight: 8,
    shadowColor: Colors.BLACK,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  assetTypeButtonSelected: {
    backgroundColor: Colors.PRIMARY,
  },
  assetTypeButtonText: {
    fontSize: 14,
    color: Colors.GRAY_700,
    marginLeft: 4,
  },
  assetTypeButtonTextSelected: {
    color: Colors.WHITE,
    fontWeight: 'bold',
  },
  portfolioContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
    marginBottom: 12,
  },
  portfolioItem: {
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: Colors.BLACK,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  portfolioItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  portfolioItemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  portfolioItemDetails: {
    marginLeft: 8,
  },
  portfolioItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
  },
  portfolioItemSymbol: {
    fontSize: 14,
    color: Colors.GRAY_600,
  },
  portfolioItemValues: {
    alignItems: 'flex-end',
  },
  portfolioItemValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
  },
  portfolioItemProfitLoss: {
    fontSize: 14,
    fontWeight: '500',
  },
  portfolioItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
    paddingTop: 12,
  },
  portfolioItemQuantity: {},
  portfolioItemQuantityLabel: {
    fontSize: 12,
    color: Colors.GRAY_600,
    marginBottom: 2,
  },
  portfolioItemQuantityValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_900,
  },
  portfolioItemPrice: {},
  portfolioItemPriceLabel: {
    fontSize: 12,
    color: Colors.GRAY_600,
    marginBottom: 2,
  },
  portfolioItemPriceValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_900,
  },
  portfolioItemAvgPrice: {},
  portfolioItemAvgPriceLabel: {
    fontSize: 12,
    color: Colors.GRAY_600,
    marginBottom: 2,
  },
  portfolioItemAvgPriceValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_900,
  },
  recentTransactionsContainer: {
    marginHorizontal: 16,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  seeAllButton: {
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    shadowColor: Colors.BLACK,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  transactionItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionTypeIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionItemDetails: {},
  transactionItemAsset: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_900,
  },
  transactionItemType: {
    fontSize: 12,
    color: Colors.GRAY_600,
  },
  transactionItemRight: {
    alignItems: 'flex-end',
  },
  transactionItemAmount: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_900,
  },
  transactionItemDate: {
    fontSize: 12,
    color: Colors.GRAY_600,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.GRAY_600,
    marginTop: 12,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyStateButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  emptyStateButtonText: {
    color: Colors.WHITE,
    fontWeight: '500',
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});
