import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { BarChart } from 'react-native-chart-kit';
import { useAppContext } from '../../context/AppContext';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/formatters';

const screenWidth = Dimensions.get('window').width;

/**
 * Gelişmiş Bar Chart Komponenti
 * Kategori analizi ve karşılaştırmalı veriler için
 */
const AdvancedBarChart = ({ 
  data, 
  title, 
  height = 220,
  showValues = true,
  currency = 'TRY',
  formatLabel = null,
  onBarPress = null,
  horizontal = false,
  showLegend = false,
  colors = null
}) => {
  const { theme } = useAppContext();

  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.CARD }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>{title}</Text>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
            Veri bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  // Chart verilerini hazırla
  const chartData = {
    labels: data.map(item => {
      if (formatLabel) {
        return formatLabel(item);
      }
      return item.label || item.category || item.name || item.period || '';
    }),
    datasets: [{
      data: data.map(item => item.value || item.total || item.amount || 0),
      colors: colors || data.map((item, index) => 
        item.color ? 
          (opacity = 1) => item.color + Math.round(opacity * 255).toString(16).padStart(2, '0') :
          (opacity = 1) => Colors.CHART_COLORS[index % Colors.CHART_COLORS.length] + Math.round(opacity * 255).toString(16).padStart(2, '0')
      )
    }]
  };

  const chartConfig = {
    backgroundColor: theme.CARD,
    backgroundGradientFrom: theme.CARD,
    backgroundGradientTo: theme.CARD,
    decimalPlaces: 0,
    color: (opacity = 1) => theme.TEXT_PRIMARY + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    labelColor: (opacity = 1) => theme.TEXT_SECONDARY + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    style: {
      borderRadius: 16,
    },
    propsForBackgroundLines: {
      strokeDasharray: "5,5",
      stroke: theme.TEXT_SECONDARY + '40',
      strokeWidth: 1,
    },
    formatYLabel: (value) => {
      const num = parseFloat(value);
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
      }
      return num.toFixed(0);
    },
    barPercentage: 0.7,
    fillShadowGradient: Colors.PRIMARY,
    fillShadowGradientOpacity: 0.8,
  };

  const handleBarPress = (data, index) => {
    if (onBarPress) {
      onBarPress(data[index], index);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.CARD }]}>
      {title && (
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>{title}</Text>
      )}
      
      <View style={styles.chartContainer}>
        <BarChart
          data={chartData}
          width={screenWidth - 40}
          height={height}
          chartConfig={chartConfig}
          style={styles.chart}
          onDataPointClick={handleBarPress}
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withInnerLines={false}
          withOuterLines={false}
          showValuesOnTopOfBars={showValues}
          fromZero={true}
          segments={4}
        />
      </View>

      {/* Detaylı değerler listesi */}
      {showValues && (
        <View style={styles.valuesContainer}>
          {data.slice(0, 5).map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.valueRow}
              onPress={() => onBarPress && onBarPress(item, index)}
            >
              <View style={styles.valueLeft}>
                <View 
                  style={[
                    styles.colorIndicator, 
                    { backgroundColor: item.color || Colors.CHART_COLORS[index % Colors.CHART_COLORS.length] }
                  ]} 
                />
                <Text style={[styles.valueLabel, { color: theme.TEXT_PRIMARY }]} numberOfLines={1}>
                  {item.label || item.category || item.name || `Item ${index + 1}`}
                </Text>
              </View>
              <View style={styles.valueRight}>
                <Text style={[styles.valueAmount, { color: theme.TEXT_PRIMARY }]}>
                  {formatCurrency(item.value || item.total || item.amount || 0, currency)}
                </Text>
                {item.percentage && (
                  <Text style={[styles.valuePercentage, { color: theme.TEXT_SECONDARY }]}>
                    {item.percentage.toFixed(1)}%
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          ))}
          
          {data.length > 5 && (
            <Text style={[styles.moreItemsText, { color: theme.TEXT_SECONDARY }]}>
              +{data.length - 5} daha fazla kategori
            </Text>
          )}
        </View>
      )}

      {/* Özet istatistikler */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam</Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(data.reduce((sum, item) => sum + (item.value || item.total || item.amount || 0), 0), currency)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Ortalama</Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(data.reduce((sum, item) => sum + (item.value || item.total || item.amount || 0), 0) / data.length, currency)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>En Yüksek</Text>
            <Text style={[styles.summaryValue, { color: Colors.SUCCESS }]}>
              {formatCurrency(Math.max(...data.map(item => item.value || item.total || item.amount || 0)), currency)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  chart: {
    borderRadius: 16,
  },
  emptyContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
  },
  valuesContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  valueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  valueLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  colorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  valueLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  valueRight: {
    alignItems: 'flex-end',
  },
  valueAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  valuePercentage: {
    fontSize: 12,
    fontWeight: '400',
    marginTop: 2,
  },
  moreItemsText: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  summaryContainer: {
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '700',
  },
});

export default AdvancedBarChart;
