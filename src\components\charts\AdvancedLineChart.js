import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { useAppContext } from '../../context/AppContext';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/formatters';

const screenWidth = Dimensions.get('window').width;

/**
 * Gelişmiş Line Chart Komponenti
 * Trend analizi ve zaman bazlı veriler için
 */
const AdvancedLineChart = ({ 
  data, 
  title, 
  height = 220,
  showLegend = true,
  showValues = false,
  currency = 'TRY',
  formatLabel = null,
  onDataPointClick = null
}) => {
  const { theme } = useAppContext();

  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.CARD }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>{title}</Text>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
            Veri bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  // Chart verilerini hazırla
  const chartData = {
    labels: data.map(item => {
      if (formatLabel) {
        return formatLabel(item.period);
      }
      // Varsayılan format
      if (item.period.includes('-')) {
        const parts = item.period.split('-');
        if (parts.length === 3) {
          return `${parts[2]}/${parts[1]}`;
        } else if (parts.length === 2) {
          return `${parts[1]}/${parts[0].slice(-2)}`;
        }
      }
      return item.period;
    }),
    datasets: [
      {
        data: data.map(item => item.income || 0),
        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`, // Yeşil - Gelir
        strokeWidth: 3,
      },
      {
        data: data.map(item => item.expense || 0),
        color: (opacity = 1) => `rgba(244, 67, 54, ${opacity})`, // Kırmızı - Gider
        strokeWidth: 3,
      },
      {
        data: data.map(item => item.balance || 0),
        color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`, // Mavi - Bakiye
        strokeWidth: 2,
        withDots: false,
      }
    ]
  };

  const chartConfig = {
    backgroundColor: theme.CARD,
    backgroundGradientFrom: theme.CARD,
    backgroundGradientTo: theme.CARD,
    decimalPlaces: 0,
    color: (opacity = 1) => theme.TEXT_PRIMARY + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    labelColor: (opacity = 1) => theme.TEXT_SECONDARY + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: "4",
      strokeWidth: "2",
    },
    propsForBackgroundLines: {
      strokeDasharray: "5,5",
      stroke: theme.TEXT_SECONDARY + '40',
      strokeWidth: 1,
    },
    formatYLabel: (value) => {
      const num = parseFloat(value);
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
      }
      return num.toFixed(0);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.CARD }]}>
      {title && (
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>{title}</Text>
      )}
      
      <View style={styles.chartContainer}>
        <LineChart
          data={chartData}
          width={screenWidth - 40}
          height={height}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
          onDataPointClick={onDataPointClick}
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withInnerLines={true}
          withOuterLines={false}
          withVerticalLines={false}
          withHorizontalLines={true}
        />
      </View>

      {showLegend && (
        <View style={styles.legend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: 'rgba(76, 175, 80, 1)' }]} />
            <Text style={[styles.legendText, { color: theme.TEXT_SECONDARY }]}>Gelir</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: 'rgba(244, 67, 54, 1)' }]} />
            <Text style={[styles.legendText, { color: theme.TEXT_SECONDARY }]}>Gider</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: 'rgba(33, 150, 243, 1)' }]} />
            <Text style={[styles.legendText, { color: theme.TEXT_SECONDARY }]}>Bakiye</Text>
          </View>
        </View>
      )}

      {showValues && data.length > 0 && (
        <View style={styles.valuesContainer}>
          <Text style={[styles.valuesTitle, { color: theme.TEXT_PRIMARY }]}>Son Değerler:</Text>
          <View style={styles.valuesRow}>
            <Text style={[styles.valueItem, { color: Colors.SUCCESS }]}>
              Gelir: {formatCurrency(data[data.length - 1].income || 0, currency)}
            </Text>
            <Text style={[styles.valueItem, { color: Colors.DANGER }]}>
              Gider: {formatCurrency(data[data.length - 1].expense || 0, currency)}
            </Text>
            <Text style={[styles.valueItem, { color: Colors.PRIMARY }]}>
              Bakiye: {formatCurrency(data[data.length - 1].balance || 0, currency)}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  chart: {
    borderRadius: 16,
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
    marginVertical: 4,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
  },
  valuesContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  valuesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  valuesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  valueItem: {
    fontSize: 12,
    fontWeight: '500',
    marginVertical: 2,
  },
});

export default AdvancedLineChart;
