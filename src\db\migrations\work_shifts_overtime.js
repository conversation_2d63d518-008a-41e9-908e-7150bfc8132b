/**
 * Vardiya tablosuna overtime_multiplier sütunu ekleyen migrasyon
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsOvertime = async (db) => {
  try {
    // Migrasyon işlemini bir transaction içinde yap
    await db.execAsync('BEGIN TRANSACTION;');

    // work_shifts tablosunu kontrol et
    const workShiftsExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='work_shifts'
    `);

    if (workShiftsExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);
      const columnNames = columns.map(col => col.name);

      // overtime_multiplier sütunu ekle
      if (!columnNames.includes('overtime_multiplier')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN overtime_multiplier REAL DEFAULT 1.5`);
      }
      
      // is_holiday sütunu ekle
      if (!columnNames.includes('is_holiday')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN is_holiday INTEGER DEFAULT 0`);
      }
      
      // holiday_multiplier sütunu ekle
      if (!columnNames.includes('holiday_multiplier')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN holiday_multiplier REAL DEFAULT 2.0`);
      }
    }

    // Transaction'ı tamamla
    await db.execAsync('COMMIT;');
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await db.execAsync('ROLLBACK;');
    console.error('Vardiya overtime migrasyonu hatası:', error);
    throw error;
  }
};
