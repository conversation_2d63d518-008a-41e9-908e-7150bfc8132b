import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useAppContext } from '../context/AppContext';
import PinAuthScreen from '../screens/PinAuthScreen';

/**
 * Kimlik Doğrulama Wrapper
 * Uygulamanın güvenlik katmanı
 */
export default function AuthWrapper({ children }) {
  const { isAuthenticated, isLoading, authSettings, performAuthentication } = useAuth();
  const { theme } = useAppContext();
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      // Eğer güvenlik ayarları yoksa direkt geçir
      if (!authSettings.pinEnabled && !authSettings.biometricEnabled) {
        setAuthChecked(true);
        return;
      }

      // Kimlik doğrulama gerekiyorsa
      if (!isAuthenticated) {
        const result = await performAuthentication();
        
        if (result.success) {
          setAuthChecked(true);
        } else if (result.requirePin) {
          // PIN gerekiyor, PinAuthScreen göster
          setAuthChecked(true);
        }
      } else {
        setAuthChecked(true);
      }
    } catch (error) {
      console.error('Kimlik doğrulama kontrolü hatası:', error);
      setAuthChecked(true);
    }
  };

  // Yükleniyor durumu
  if (isLoading || !authChecked) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
      </View>
    );
  }

  // Kimlik doğrulama gerekiyor
  if (!isAuthenticated && (authSettings.pinEnabled || authSettings.biometricEnabled)) {
    return <PinAuthScreen mode="auth" />;
  }

  // Kimlik doğrulandı, ana uygulamayı göster
  return children;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
