import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, TextInput, Modal } from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { formatCurrency } from '../utils/formatters';

/**
 * Bütçe yönetimi ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @returns {JSX.Element} Bütçe yönetimi ekranı
 */
export default function BudgetScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const [budgets, setBudgets] = useState([]);
  const [categories, setCategories] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [newBudget, setNewBudget] = useState({
    category_id: null,
    category_name: '',
    amount: '',
    period: 'monthly', // monthly, weekly, yearly
    start_date: new Date().toISOString().split('T')[0],
    end_date: '',
    notes: ''
  });

  // Verileri yükle
  useEffect(() => {
    loadData();
  }, []);

  /**
   * Tüm verileri yükler
   */
  const loadData = async () => {
    try {
      setRefreshing(true);
      await Promise.all([
        loadBudgets(),
        loadCategories()
      ]);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    } finally {
      setRefreshing(false);
    }
  };

  /**
   * Bütçe verilerini yükler
   */
  const loadBudgets = async () => {
    try {
      // Bütçe tablosunu oluştur (eğer yoksa)
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budgets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          category_id INTEGER,
          amount DECIMAL(10,2) NOT NULL,
          period TEXT NOT NULL,
          start_date DATE NOT NULL,
          end_date DATE,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY(category_id) REFERENCES categories(id)
        )
      `);
      
      // Bütçeleri getir
      const result = await db.getAllAsync(`
        SELECT b.*, c.name as category_name, c.icon, c.color
        FROM budgets b
        LEFT JOIN categories c ON b.category_id = c.id
        ORDER BY b.created_at DESC
      `);
      
      // Her bütçe için harcama verilerini getir
      const budgetsWithSpending = await Promise.all((result || []).map(async (budget) => {
        let spending = 0;
        
        // Bütçe dönemine göre tarih aralığını belirle
        let startDate = budget.start_date;
        let endDate = budget.end_date || new Date().toISOString().split('T')[0];
        
        if (!budget.end_date) {
          if (budget.period === 'monthly') {
            const start = new Date(budget.start_date);
            const end = new Date(start);
            end.setMonth(end.getMonth() + 1);
            endDate = end.toISOString().split('T')[0];
          } else if (budget.period === 'weekly') {
            const start = new Date(budget.start_date);
            const end = new Date(start);
            end.setDate(end.getDate() + 7);
            endDate = end.toISOString().split('T')[0];
          } else if (budget.period === 'yearly') {
            const start = new Date(budget.start_date);
            const end = new Date(start);
            end.setFullYear(end.getFullYear() + 1);
            endDate = end.toISOString().split('T')[0];
          }
        }
        
        // Kategori için harcamaları getir
        if (budget.category_id) {
          const spendingResult = await db.getFirstAsync(`
            SELECT SUM(amount) as total
            FROM transactions
            WHERE category_id = ? AND type = 'expense'
            AND date BETWEEN ? AND ?
          `, [budget.category_id, startDate, endDate]);
          
          spending = spendingResult?.total || 0;
        }
        
        // Bütçe kullanım yüzdesini hesapla
        const percentage = budget.amount > 0 ? (spending / budget.amount) * 100 : 0;
        
        return {
          ...budget,
          spending,
          percentage: Math.min(percentage, 100), // Maksimum %100 olsun
          status: percentage >= 100 ? 'exceeded' : percentage >= 80 ? 'warning' : 'good'
        };
      }));
      
      setBudgets(budgetsWithSpending);
    } catch (error) {
      console.error('Bütçe verileri yükleme hatası:', error);
      throw error;
    }
  };

  /**
   * Kategori verilerini yükler
   */
  const loadCategories = async () => {
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM categories
        WHERE type = 'expense' OR type = 'both'
        ORDER BY name
      `);
      
      setCategories(result || []);
    } catch (error) {
      console.error('Kategori verileri yükleme hatası:', error);
      throw error;
    }
  };

  /**
   * Yeni bütçe ekler
   */
  const addBudget = async () => {
    try {
      if (!newBudget.category_id) {
        Alert.alert('Hata', 'Lütfen bir kategori seçin.');
        return;
      }
      
      if (!newBudget.amount || parseFloat(newBudget.amount) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir bütçe tutarı girin.');
        return;
      }
      
      // Bütçe dönemine göre bitiş tarihini hesapla
      let endDate = newBudget.end_date;
      
      if (!endDate) {
        const startDate = new Date(newBudget.start_date);
        
        if (newBudget.period === 'monthly') {
          const end = new Date(startDate);
          end.setMonth(end.getMonth() + 1);
          end.setDate(end.getDate() - 1);
          endDate = end.toISOString().split('T')[0];
        } else if (newBudget.period === 'weekly') {
          const end = new Date(startDate);
          end.setDate(end.getDate() + 6);
          endDate = end.toISOString().split('T')[0];
        } else if (newBudget.period === 'yearly') {
          const end = new Date(startDate);
          end.setFullYear(end.getFullYear() + 1);
          end.setDate(end.getDate() - 1);
          endDate = end.toISOString().split('T')[0];
        }
      }
      
      // Bütçeyi ekle
      await db.runAsync(`
        INSERT INTO budgets (category_id, amount, period, start_date, end_date, notes)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        newBudget.category_id,
        parseFloat(newBudget.amount),
        newBudget.period,
        newBudget.start_date,
        endDate,
        newBudget.notes
      ]);
      
      // Formu temizle ve kapat
      setNewBudget({
        category_id: null,
        category_name: '',
        amount: '',
        period: 'monthly',
        start_date: new Date().toISOString().split('T')[0],
        end_date: '',
        notes: ''
      });
      setShowAddForm(false);
      
      // Verileri yeniden yükle
      loadBudgets();
      
      Alert.alert('Başarılı', 'Bütçe başarıyla eklendi.');
    } catch (error) {
      console.error('Bütçe ekleme hatası:', error);
      Alert.alert('Hata', 'Bütçe eklenirken bir hata oluştu.');
    }
  };

  /**
   * Bütçe siler
   * 
   * @param {number} id - Silinecek bütçe ID'si
   */
  const deleteBudget = async (id) => {
    try {
      Alert.alert(
        'Onay',
        'Bu bütçeyi silmek istediğinize emin misiniz?',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Sil',
            style: 'destructive',
            onPress: async () => {
              await db.runAsync('DELETE FROM budgets WHERE id = ?', [id]);
              loadBudgets();
              Alert.alert('Başarılı', 'Bütçe silindi.');
            }
          }
        ]
      );
    } catch (error) {
      console.error('Bütçe silme hatası:', error);
      Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
    }
  };

  /**
   * Kategori seçer
   * 
   * @param {Object} category - Seçilen kategori
   */
  const selectCategory = (category) => {
    setNewBudget({
      ...newBudget,
      category_id: category.id,
      category_name: category.name
    });
    setShowCategoryModal(false);
  };

  /**
   * Bütçe dönemi adını formatlar
   * 
   * @param {string} period - Bütçe dönemi
   * @returns {string} Formatlanmış bütçe dönemi
   */
  const formatPeriod = (period) => {
    switch (period) {
      case 'monthly': return 'Aylık';
      case 'weekly': return 'Haftalık';
      case 'yearly': return 'Yıllık';
      default: return period;
    }
  };

  /**
   * Bütçe durumuna göre renk döndürür
   * 
   * @param {string} status - Bütçe durumu
   * @returns {string} Renk kodu
   */
  const getStatusColor = (status) => {
    switch (status) {
      case 'exceeded': return '#ff6b6b';
      case 'warning': return '#ffa502';
      case 'good': return '#2ecc71';
      default: return Colors.PRIMARY;
    }
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Bütçe Yönetimi</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddForm(!showAddForm)}
        >
          <MaterialIcons name={showAddForm ? 'close' : 'add'} size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {showAddForm && (
        <View style={styles.addForm}>
          <Text style={styles.formTitle}>Yeni Bütçe Ekle</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Kategori</Text>
            <TouchableOpacity
              style={styles.categorySelector}
              onPress={() => setShowCategoryModal(true)}
            >
              <Text style={[
                styles.categoryText,
                !newBudget.category_name && { color: '#999' }
              ]}>
                {newBudget.category_name || 'Kategori seçin'}
              </Text>
              <MaterialIcons name="arrow-drop-down" size={24} color="#999" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Bütçe Tutarı</Text>
            <TextInput
              style={styles.input}
              value={newBudget.amount}
              onChangeText={(text) => setNewBudget({ ...newBudget, amount: text })}
              placeholder="Bütçe tutarı"
              keyboardType="numeric"
              placeholderTextColor="#999"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Dönem</Text>
            <View style={styles.periodSelector}>
              {['monthly', 'weekly', 'yearly'].map((period) => (
                <TouchableOpacity
                  key={period}
                  style={[
                    styles.periodOption,
                    newBudget.period === period && styles.periodOptionSelected
                  ]}
                  onPress={() => setNewBudget({ ...newBudget, period })}
                >
                  <Text style={[
                    styles.periodOptionText,
                    newBudget.period === period && styles.periodOptionTextSelected
                  ]}>
                    {formatPeriod(period)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Başlangıç Tarihi</Text>
            <TextInput
              style={styles.input}
              value={newBudget.start_date}
              onChangeText={(text) => setNewBudget({ ...newBudget, start_date: text })}
              placeholder="YYYY-AA-GG"
              placeholderTextColor="#999"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Notlar</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={newBudget.notes}
              onChangeText={(text) => setNewBudget({ ...newBudget, notes: text })}
              placeholder="Bütçe hakkında notlar"
              placeholderTextColor="#999"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>
          
          <TouchableOpacity
            style={styles.submitButton}
            onPress={addBudget}
          >
            <Text style={styles.submitButtonText}>Bütçe Ekle</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView style={styles.content}>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Bütçe Özeti</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Toplam Bütçe:</Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(budgets.reduce((sum, b) => sum + b.amount, 0))}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Toplam Harcama:</Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(budgets.reduce((sum, b) => sum + b.spending, 0))}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Kalan Bütçe:</Text>
            <Text style={[
              styles.summaryValue,
              { color: budgets.reduce((sum, b) => sum + b.amount, 0) - budgets.reduce((sum, b) => sum + b.spending, 0) < 0 ? '#ff6b6b' : '#2ecc71' }
            ]}>
              {formatCurrency(budgets.reduce((sum, b) => sum + b.amount, 0) - budgets.reduce((sum, b) => sum + b.spending, 0))}
            </Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Aktif Bütçeler</Text>
        
        {budgets.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialIcons name="account-balance-wallet" size={48} color="#ccc" />
            <Text style={styles.emptyStateText}>Henüz bütçe bulunmuyor.</Text>
            <Text style={styles.emptyStateSubtext}>Bütçe eklemek için sağ üstteki + butonuna tıklayın.</Text>
          </View>
        ) : (
          budgets.map((budget) => (
            <View key={budget.id} style={styles.budgetCard}>
              <View style={styles.budgetHeader}>
                <View style={styles.budgetCategory}>
                  <MaterialIcons 
                    name={budget.icon || "category"} 
                    size={20} 
                    color={budget.color || Colors.PRIMARY} 
                  />
                  <Text style={styles.budgetCategoryName}>{budget.category_name || 'Kategori'}</Text>
                </View>
                <TouchableOpacity
                  onPress={() => deleteBudget(budget.id)}
                  style={styles.deleteButton}
                >
                  <MaterialIcons name="delete" size={20} color="#ff6b6b" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.budgetInfo}>
                <Text style={styles.budgetPeriod}>
                  {formatPeriod(budget.period)} ({budget.start_date} - {budget.end_date || 'Devam ediyor'})
                </Text>
                <Text style={styles.budgetAmount}>{formatCurrency(budget.amount)}</Text>
              </View>
              
              <View style={styles.budgetProgress}>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { 
                        width: `${budget.percentage}%`,
                        backgroundColor: getStatusColor(budget.status)
                      }
                    ]} 
                  />
                </View>
                <View style={styles.progressInfo}>
                  <Text style={styles.progressText}>
                    {formatCurrency(budget.spending)} / {formatCurrency(budget.amount)}
                  </Text>
                  <Text style={[styles.progressPercentage, { color: getStatusColor(budget.status) }]}>
                    {Math.round(budget.percentage)}%
                  </Text>
                </View>
              </View>
              
              {budget.notes && (
                <Text style={styles.budgetNotes}>{budget.notes}</Text>
              )}
            </View>
          ))
        )}
      </ScrollView>

      {/* Kategori Seçim Modalı */}
      <Modal
        visible={showCategoryModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Kategori Seçin</Text>
              <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
                <MaterialIcons name="close" size={24} color="#999" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.categoryList}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={styles.categoryItem}
                  onPress={() => selectCategory(category)}
                >
                  <View style={styles.categoryIcon}>
                    <MaterialIcons 
                      name={category.icon || "category"} 
                      size={24} 
                      color={category.color || Colors.PRIMARY} 
                    />
                  </View>
                  <Text style={styles.categoryItemName}>{category.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  addForm: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333333',
  },
  formGroup: {
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    marginBottom: 4,
    color:'#333333',
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333333',
  },
  textArea: {
    minHeight: 80,
  },
  categorySelector: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 16,
    color: '#333333',
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    overflow: 'hidden',
  },
  periodOption: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
  },
  periodOptionSelected: {
    backgroundColor: Colors.PRIMARY,
  },
  periodOptionText: {
    fontSize: 14,
    color: '#333333',
  },
  periodOptionTextSelected: {
    color: '#fff',
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    padding: 14,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333333',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color:'#333333',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333333',
  },
  budgetCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  budgetCategory: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetCategoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#333333',
  },
  deleteButton: {
    padding: 4,
  },
  budgetInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  budgetPeriod: {
    fontSize: 14,
    color:'#333333',
  },
  budgetAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  budgetProgress: {
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressText: {
    fontSize: 12,
    color:'#333333',
  },
  progressPercentage: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  budgetNotes: {
    fontSize: 14,
    color:'#333333',
    marginTop: 8,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: 'bold',
    color:'#333333',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color:'#333333',
    textAlign: 'center',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  categoryList: {
    padding: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryItemName: {
    fontSize: 16,
    color: '#333333',
  },
});
