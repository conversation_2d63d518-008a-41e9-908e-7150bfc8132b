import React, { createContext, useState, useContext, useEffect } from 'react';
import { AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const SecurityContext = createContext();

/**
 * Uygulama güvenliği için context provider
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Çocuk bileşenler
 * @returns {JSX.Element} SecurityProvider komponenti
 */
export const SecurityProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [inactivityTimeout, setInactivityTimeout] = useState(300000); // 5 dakika (varsayılan)
  const [backgroundTime, setBackgroundTime] = useState(null);

  // PIN güvenlik ayarlarını yükle
  useEffect(() => {
    const loadSecuritySettings = async () => {
      try {
        // Güvenlik zaman aşımı ayarını yükle
        const timeout = await AsyncStorage.getItem('securityTimeout');
        if (timeout !== null) {
          setInactivityTimeout(parseInt(timeout));
        }
      } catch (error) {
        console.error('Güvenlik ayarları yüklenirken hata:', error);
      }
    };

    loadSecuritySettings();
  }, []);

  // Kimlik doğrulama durumunu kontrol et
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);
        const sessionActive = await AsyncStorage.getItem('sessionActive');
        setIsAuthenticated(sessionActive === 'true');
      } catch (error) {
        console.error('Oturum durumu kontrol hatası:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();

    // AppState değişikliklerini dinle
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      subscription.remove();
    };
  }, []);

  /**
   * Uygulama durum değişikliğini yönet
   * @param {string} nextAppState Yeni uygulama durumu
   */
  const handleAppStateChange = async (nextAppState) => {
    try {
      if (nextAppState === 'active') {
        // Uygulama aktif olduğunda ve arka planda kalma süresi yeterince uzunsa PIN iste
        if (backgroundTime) {
          const now = Date.now();
          const timeInBackground = now - backgroundTime;
          
          if (timeInBackground > inactivityTimeout) {
            await lockApp();
          }
        }
        setBackgroundTime(null);
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Arka plana geçiş zamanını kaydet
        setBackgroundTime(Date.now());
      }
    } catch (error) {
      console.error('Uygulama durum değişikliği hatası:', error);
    }
  };

  /**
   * Kimlik doğrulama işlemi
   */
  const authenticate = async () => {
    try {
      await AsyncStorage.setItem('sessionActive', 'true');
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Kimlik doğrulama hatası:', error);
    }
  };

  /**
   * Uygulamayı kilitle
   */
  const lockApp = async () => {
    try {
      // Bu fonksiyon MainApp'e navigasyon yapmaya çalışmayacak,
      // sadece oturum durumunu false olarak ayarlayacak
      await AsyncStorage.setItem('sessionActive', 'false');
      setIsAuthenticated(false);
      
      // NavigationContainer'ı bu değişiklik hakkında bilgilendir
      console.log('Uygulama kilitlendi: Oturum durumu false olarak ayarlandı');
    } catch (error) {
      console.error('Uygulama kilitleme hatası:', error);
    }
  };

  /**
   * Güvenlik zaman aşımını ayarla
   * @param {number} timeout Zaman aşımı süresi (ms)
   */
  const setSecurityTimeout = async (timeout) => {
    try {
      await AsyncStorage.setItem('securityTimeout', timeout.toString());
      setInactivityTimeout(timeout);
    } catch (error) {
      console.error('Güvenlik zaman aşımı ayarlama hatası:', error);
    }
  };

  const value = {
    isAuthenticated,
    isLoading,
    authenticate,
    lockApp,
    inactivityTimeout,
    setSecurityTimeout
  };

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
};

/**
 * Güvenlik bağlamını kullanmak için hook
 * @returns {Object} Güvenlik bağlamı değerleri ve fonksiyonları
 */
export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (context === undefined) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};
