import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { MaterialIcons } from '@expo/vector-icons';
import { useTransactionService } from '../db/dbService';
import { Colors } from '../constants/colors';
import { useExchangeRates } from '../contexts/ExchangeRateContext';

// Bileşenler
import TransactionItem from '../components/transactions/TransactionItem';
import TransactionFilter from '../components/transactions/TransactionFilter';
import LoadingIndicator from '../components/common/LoadingIndicator';
import EmptyState from '../components/common/EmptyState';

/**
 * İşlem yönetim ekranı
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} İşlem yönetim ekranı
 */
const TransactionScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const transactionService = useTransactionService();
  const { rates } = useExchangeRates();

  // URL parametrelerini al
  const params = route?.params || {};
  const initialType = params.type || 'expense';

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [selectedType, setSelectedType] = useState(initialType); // 'expense' or 'income'
  const [stats, setStats] = useState({
    totalAmount: 0,
    count: 0
  });
  const [filters, setFilters] = useState({
    startDate: null,
    endDate: null,
    categoryId: null,
    minAmount: null,
    maxAmount: null
  });
  const [showFilters, setShowFilters] = useState(false);

  // İşlemleri yükle
  const loadTransactions = useCallback(async () => {
    try {
      setIsLoading(true);

      // İşlemleri getir
      const data = await transactionService.getTransactions({
        isIncome: selectedType === 'income'
      });

      // İşlemleri ayarla
      setTransactions(data);

      // İstatistikleri hesapla
      if (data.length > 0) {
        const total = data.reduce((sum, tx) => sum + tx.amount, 0);
        setStats({
          totalAmount: total,
          count: data.length
        });
      } else {
        setStats({
          totalAmount: 0,
          count: 0
        });
      }
    } catch (error) {
      console.error('İşlemler yüklenirken hata:', error);
      Alert.alert('Hata', 'İşlemler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [transactionService, selectedType]);

  // Yenileme işlemi
  const handleRefresh = () => {
    setRefreshing(true);
    loadTransactions();
  };

  // İşlem tipi değiştiğinde işlemleri yeniden yükle
  useEffect(() => {
    loadTransactions();
  }, [loadTransactions, selectedType]);

  // İşlem silme
  const handleDeleteTransaction = useCallback(async (id) => {
    if (!id) return;

    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await transactionService.deleteTransaction(id);
              loadTransactions();
            } catch (error) {
              console.error('İşlem silinirken hata:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  }, [transactionService, loadTransactions]);

  // Yeni işlem ekleme
  const handleAddTransaction = useCallback(() => {
    navigation.navigate('transaction/edit', {
      type: selectedType
    });
  }, [navigation, selectedType]);

  // İşlem düzenleme
  const handleEditTransaction = useCallback((transaction) => {
    if (!transaction?.id) return;

    navigation.navigate('transaction/edit', {
      id: transaction.id,
      type: selectedType
    });
  }, [navigation, selectedType]);

  // İşlem öğesine tıklandığında
  const handleTransactionPress = useCallback((transaction) => {
    if (!transaction?.id) return;

    // İşlem detaylarını göster
    navigation.navigate('TransactionDetail', {
      transactionId: transaction.id
    });
  }, [navigation]);

  // Para birimini formatla
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // İşlem öğesi
  const renderTransactionItem = ({ item }) => (
    <TransactionItem
      transaction={item}
      onPress={() => handleTransactionPress(item)}
      onDelete={() => handleDeleteTransaction(item.id)}
    />
  );

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>İşlemler</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('transaction/edit', { type: selectedType })}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* İşlem Tipi Seçici */}
      <View style={styles.typeSelector}>
        <TouchableOpacity
          style={[
            styles.typeOption,
            selectedType === 'expense' && styles.activeTypeOption
          ]}
          onPress={() => setSelectedType('expense')}
        >
          <MaterialIcons
            name="remove-circle"
            size={20}
            color={selectedType === 'expense' ? '#fff' : '#757575'}
          />
          <Text
            style={[
              styles.typeText,
              selectedType === 'expense' && styles.activeTypeText
            ]}
          >
            Giderler
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeOption,
            selectedType === 'income' && styles.activeTypeOption,
            selectedType === 'income' && { backgroundColor: '#2ecc71' }
          ]}
          onPress={() => setSelectedType('income')}
        >
          <MaterialIcons
            name="add-circle"
            size={20}
            color={selectedType === 'income' ? '#fff' : '#757575'}
          />
          <Text
            style={[
              styles.typeText,
              selectedType === 'income' && styles.activeTypeText
            ]}
          >
            Gelirler
          </Text>
        </TouchableOpacity>
      </View>

      {/* Toplam Özeti */}
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>
          Toplam {selectedType === 'income' ? 'Gelir' : 'Gider'}
        </Text>
        <Text style={[
          styles.summaryAmount,
          { color: selectedType === 'income' ? '#2ecc71' : '#e74c3c' }
        ]}>
          {formatCurrency(stats.totalAmount)}
        </Text>
        <Text style={styles.summaryCount}>
          {stats.count} işlem
        </Text>
      </View>

      {/* İşlem Listesi */}
      {transactions.length > 0 ? (
        <FlatList
          data={transactions}
          renderItem={renderTransactionItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      ) : (
        <EmptyState
          icon={selectedType === 'income' ? 'account-balance-wallet' : 'receipt-long'}
          message={`Henüz ${selectedType === 'income' ? 'gelir' : 'gider'} kaydı bulunmuyor`}
          actionText={`Yeni ${selectedType === 'income' ? 'Gelir' : 'Gider'} Ekle`}
          onAction={() => navigation.navigate('transaction/edit', { type: selectedType })}
        />
      )}

      {/* Yeni İşlem Butonu */}
      <TouchableOpacity
        style={[
          styles.floatingButton,
          { backgroundColor: selectedType === 'income' ? '#2ecc71' : '#e74c3c' }
        ]}
        onPress={() => navigation.navigate('transaction/edit', { type: selectedType })}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeSelector: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  typeOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    backgroundColor: '#f0f0f0',
  },
  activeTypeOption: {
    backgroundColor: '#e74c3c',
  },
  typeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#757575',
    marginLeft: 8,
  },
  activeTypeText: {
    color: '#fff',
    fontWeight: '600',
  },
  summaryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryCount: {
    fontSize: 12,
    color: '#757575',
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});

export default TransactionScreen;
