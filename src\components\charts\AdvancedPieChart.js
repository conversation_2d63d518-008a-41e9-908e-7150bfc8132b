import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { PieChart } from 'react-native-chart-kit';
import { useAppContext } from '../../context/AppContext';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/formatters';

const screenWidth = Dimensions.get('window').width;

/**
 * Gelişmiş Pie Chart Komponenti
 * Kategori dağılımı ve yüzdelik analiz için
 */
const AdvancedPieChart = ({ 
  data, 
  title, 
  height = 220,
  showLegend = true,
  showPercentages = true,
  currency = 'TRY',
  onSlicePress = null,
  centerText = null,
  colors = null
}) => {
  const { theme } = useAppContext();

  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.CARD }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>{title}</Text>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
            Veri bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  // Toplam değeri hesapla
  const total = data.reduce((sum, item) => sum + (item.value || item.total || item.amount || 0), 0);

  // Chart verilerini hazırla
  const chartData = data.map((item, index) => {
    const value = item.value || item.total || item.amount || 0;
    const percentage = total > 0 ? (value / total) * 100 : 0;
    
    return {
      name: item.name || item.category || item.label || `Item ${index + 1}`,
      population: value,
      color: item.color || colors?.[index] || Colors.CHART_COLORS[index % Colors.CHART_COLORS.length],
      legendFontColor: theme.TEXT_SECONDARY,
      legendFontSize: 12,
      percentage: percentage
    };
  }).filter(item => item.population > 0); // Sıfır değerleri filtrele

  const chartConfig = {
    backgroundColor: theme.CARD,
    backgroundGradientFrom: theme.CARD,
    backgroundGradientTo: theme.CARD,
    color: (opacity = 1) => theme.TEXT_PRIMARY + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    labelColor: (opacity = 1) => theme.TEXT_SECONDARY + Math.round(opacity * 255).toString(16).padStart(2, '0'),
    style: {
      borderRadius: 16,
    },
  };

  const handleSlicePress = (slice, index) => {
    if (onSlicePress) {
      onSlicePress(slice, index);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.CARD }]}>
      {title && (
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>{title}</Text>
      )}
      
      <View style={styles.chartContainer}>
        <PieChart
          data={chartData}
          width={screenWidth - 40}
          height={height}
          chartConfig={chartConfig}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="15"
          center={[10, 0]}
          absolute={showPercentages}
          hasLegend={false} // Kendi legend'ımızı kullanacağız
          onDataPointClick={handleSlicePress}
        />
        
        {centerText && (
          <View style={styles.centerTextContainer}>
            <Text style={[styles.centerText, { color: theme.TEXT_PRIMARY }]}>
              {centerText}
            </Text>
          </View>
        )}
      </View>

      {showLegend && (
        <View style={styles.legendContainer}>
          {chartData.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.legendItem}
              onPress={() => handleSlicePress(item, index)}
            >
              <View style={styles.legendLeft}>
                <View style={[styles.legendDot, { backgroundColor: item.color }]} />
                <Text style={[styles.legendName, { color: theme.TEXT_PRIMARY }]} numberOfLines={1}>
                  {item.name}
                </Text>
              </View>
              <View style={styles.legendRight}>
                <Text style={[styles.legendValue, { color: theme.TEXT_PRIMARY }]}>
                  {formatCurrency(item.population, currency)}
                </Text>
                {showPercentages && (
                  <Text style={[styles.legendPercentage, { color: theme.TEXT_SECONDARY }]}>
                    {item.percentage.toFixed(1)}%
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Özet bilgiler */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam</Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(total, currency)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Kategori</Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {chartData.length}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>En Büyük</Text>
            <Text style={[styles.summaryValue, { color: Colors.SUCCESS }]}>
              {chartData.length > 0 ? chartData[0].percentage.toFixed(1) + '%' : '0%'}
            </Text>
          </View>
        </View>
      </View>

      {/* En büyük 3 kategori */}
      {chartData.length > 3 && (
        <View style={styles.topCategoriesContainer}>
          <Text style={[styles.topCategoriesTitle, { color: theme.TEXT_PRIMARY }]}>
            En Büyük 3 Kategori
          </Text>
          {chartData
            .sort((a, b) => b.population - a.population)
            .slice(0, 3)
            .map((item, index) => (
              <View key={index} style={styles.topCategoryItem}>
                <View style={styles.topCategoryLeft}>
                  <Text style={[styles.topCategoryRank, { color: theme.PRIMARY }]}>
                    #{index + 1}
                  </Text>
                  <View style={[styles.topCategoryDot, { backgroundColor: item.color }]} />
                  <Text style={[styles.topCategoryName, { color: theme.TEXT_PRIMARY }]}>
                    {item.name}
                  </Text>
                </View>
                <View style={styles.topCategoryRight}>
                  <Text style={[styles.topCategoryValue, { color: theme.TEXT_PRIMARY }]}>
                    {formatCurrency(item.population, currency)}
                  </Text>
                  <Text style={[styles.topCategoryPercentage, { color: theme.TEXT_SECONDARY }]}>
                    {item.percentage.toFixed(1)}%
                  </Text>
                </View>
              </View>
            ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  centerTextContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerText: {
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },
  emptyContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
  },
  legendContainer: {
    marginTop: 12,
  },
  legendItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 4,
  },
  legendLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendName: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  legendRight: {
    alignItems: 'flex-end',
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  legendPercentage: {
    fontSize: 12,
    fontWeight: '400',
    marginTop: 2,
  },
  summaryContainer: {
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '700',
  },
  topCategoriesContainer: {
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  topCategoriesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  topCategoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  topCategoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  topCategoryRank: {
    fontSize: 14,
    fontWeight: '700',
    marginRight: 8,
    minWidth: 24,
  },
  topCategoryDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  topCategoryName: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  topCategoryRight: {
    alignItems: 'flex-end',
  },
  topCategoryValue: {
    fontSize: 13,
    fontWeight: '600',
  },
  topCategoryPercentage: {
    fontSize: 11,
    fontWeight: '400',
    marginTop: 1,
  },
});

export default AdvancedPieChart;
