/**
 * Döviz kuru servisi
 * API: https://api.exchangerate-api.com/v4/latest/TRY
 */

/**
 * Döviz kurlarını getirir
 *
 * @param {string} baseCurrency - Baz para birimi (varsayılan: TRY)
 * @returns {Promise<Object>} Döviz kurları
 */
export const fetchExchangeRates = async (baseCurrency = 'TRY') => {
  try {
    // Parametreleri kontrol et
    if (!baseCurrency) {
      console.error('Base currency is undefined or null');
      baseCurrency = 'TRY'; // Varsayılan değer
    }

    console.log(`Fetching exchange rates from API for ${baseCurrency}`);

    // Gerçek API çağrısı
    const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${baseCurrency}`);

    if (!response.ok) {
      throw new Error(`Döviz kurları alınamadı: ${response.status}`);
    }

    const data = await response.json();

    // API yanıtını kontrol et
    if (!data || !data.rates) {
      console.error('API did not return valid data:', data);
      throw new Error('Invalid API response');
    }

    // API yanıtını doğru formata dönüştür
    const result = {
      base: data.base || baseCurrency,
      date: data.date || new Date().toISOString().split('T')[0],
      rates: data.rates
    };

    console.log(`API returned data for ${result.base} with ${Object.keys(result.rates).length} rates`);

    return result;
  } catch (error) {
    console.error('Döviz kuru servisi hatası:', error);
    throw error;
  }
};

/**
 * Döviz kurlarını veritabanına kaydeder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} rates - Döviz kurları
 * @param {string} baseCurrency - Baz para birimi
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Promise<void>}
 */
export const saveExchangeRates = async (db, rates, baseCurrency, date) => {
  try {
    // Parametreleri kontrol et
    if (!baseCurrency) {
      console.error('Base currency is undefined or null:', baseCurrency);
      baseCurrency = 'TRY'; // Varsayılan değer kullan
    }

    if (!date) {
      console.error('Date is undefined or null:', date);
      date = new Date().toISOString().split('T')[0]; // Bugünün tarihini kullan
    }

    if (!rates || typeof rates !== 'object') {
      console.error('Rates is not a valid object:', rates);
      throw new Error('Rates must be a valid object');
    }

    console.log('Saving exchange rates:', {
      baseCurrency,
      date,
      ratesCount: Object.keys(rates).length
    });

    await db.withTransactionAsync(async () => {
      try {
        // Önce eski kurları temizle
        await db.runAsync(`
          DELETE FROM exchange_rates
          WHERE base_currency = ? AND date = ?
        `, [baseCurrency, date]);

        // Yeni kurları ekle
        for (const [currency, rate] of Object.entries(rates)) {
          try {
            console.log(`Inserting rate for ${baseCurrency} -> ${currency}: ${rate}`);

            await db.runAsync(`
              INSERT INTO exchange_rates (
                base_currency, target_currency, rate, date
              )
              VALUES (?, ?, ?, ?)
            `, [
              baseCurrency,
              currency,
              rate,
              date
            ]);
          } catch (insertError) {
            console.error(`Error inserting rate for ${currency}:`, insertError);
            // Devam et, diğer kurları kaydetmeye çalış
          }
        }
      } catch (transactionError) {
        console.error('Transaction error:', transactionError);
        throw transactionError;
      }
    });

    console.log('Exchange rates saved successfully');
  } catch (error) {
    console.error('Döviz kurları kaydetme hatası:', error);
    throw error;
  }
};

/**
 * Döviz kurlarını veritabanından alır
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} baseCurrency - Baz para birimi
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Promise<Object>} Döviz kurları
 */
export const getExchangeRates = async (db, baseCurrency = 'TRY', date = null) => {
  try {
    // Parametreleri kontrol et
    if (!baseCurrency) {
      console.error('Base currency is undefined or null');
      baseCurrency = 'TRY'; // Varsayılan değer
    }

    // Tarih belirtilmemişse bugünün tarihini kullan
    const targetDate = date || new Date().toISOString().split('T')[0];

    console.log(`Getting exchange rates for ${baseCurrency} on ${targetDate}`);

    try {
      // Veritabanından kurları al
      const rates = await db.getAllAsync(`
        SELECT target_currency, rate
        FROM exchange_rates
        WHERE base_currency = ? AND date = ?
      `, [baseCurrency, targetDate]);

      console.log(`Found ${rates.length} rates in database`);

      // Sonuçları nesne formatına dönüştür
      const ratesObject = {};
      rates.forEach(rate => {
        ratesObject[rate.target_currency] = rate.rate;
      });

      // Eğer kur bulunamadıysa API'den al ve kaydet
      if (Object.keys(ratesObject).length === 0) {
        console.log(`No rates found in database, fetching from API for ${baseCurrency}`);

        try {
          const apiRates = await fetchExchangeRates(baseCurrency);

          if (apiRates && apiRates.rates) {
            console.log(`API returned ${Object.keys(apiRates.rates).length} rates`);

            try {
              await saveExchangeRates(db, apiRates.rates, apiRates.base || baseCurrency, apiRates.date || targetDate);
              return apiRates.rates;
            } catch (saveError) {
              console.error('Error saving exchange rates:', saveError);
              return apiRates.rates; // Kaydetme hatası olsa bile API'den gelen kurları döndür
            }
          } else {
            console.error('API did not return valid rates:', apiRates);
            return {}; // Boş nesne döndür
          }
        } catch (apiError) {
          console.error('API fetch error:', apiError);
          return {}; // Boş nesne döndür
        }
      }

      return ratesObject;
    } catch (dbError) {
      console.error('Database error when getting exchange rates:', dbError);

      // Veritabanı hatası durumunda API'den almayı dene
      try {
        const apiRates = await fetchExchangeRates(baseCurrency);
        return apiRates?.rates || {};
      } catch (fallbackError) {
        console.error('Fallback API fetch error:', fallbackError);
        return {}; // Boş nesne döndür
      }
    }
  } catch (error) {
    console.error('Döviz kurları getirme hatası:', error);
    return {}; // Boş nesne döndür
  }
};

/**
 * Belirli bir para biriminden diğerine dönüşüm yapar
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} amount - Miktar
 * @param {string} fromCurrency - Kaynak para birimi
 * @param {string} toCurrency - Hedef para birimi
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Promise<number>} Dönüştürülmüş miktar
 */
export const convertCurrency = async (db, amount, fromCurrency, toCurrency, date = null) => {
  try {
    // Parametreleri kontrol et
    if (!db) {
      throw new Error('Database connection is required');
    }

    if (amount == null || isNaN(amount)) {
      console.error('Invalid amount:', amount);
      return 0;
    }

    if (!fromCurrency) {
      console.error('Source currency is undefined or null');
      fromCurrency = 'TRY'; // Varsayılan değer
    }

    if (!toCurrency) {
      console.error('Target currency is undefined or null');
      toCurrency = 'TRY'; // Varsayılan değer
    }

    // Aynı para birimi ise doğrudan döndür
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // Tarih belirtilmemişse bugünün tarihini kullan
    const targetDate = date || new Date().toISOString().split('T')[0];

    console.log(`Converting ${amount} ${fromCurrency} to ${toCurrency} on ${targetDate}`);

    try {
      // Kaynak para birimi için kurları al
      const fromRates = await getExchangeRates(db, fromCurrency, targetDate);

      // Hedef para birimi için dönüşüm yap
      if (fromRates && fromRates[toCurrency]) {
        const result = amount * fromRates[toCurrency];
        console.log(`Direct conversion: ${amount} ${fromCurrency} = ${result} ${toCurrency}`);
        return result;
      }

      // Eğer doğrudan dönüşüm yoksa, TRY üzerinden dönüşüm yap
      console.log(`No direct conversion, trying via TRY`);
      const tryRates = await getExchangeRates(db, 'TRY', targetDate);

      if (tryRates && tryRates[fromCurrency] && tryRates[toCurrency]) {
        const amountInTRY = amount / tryRates[fromCurrency];
        const result = amountInTRY * tryRates[toCurrency];
        console.log(`Indirect conversion via TRY: ${amount} ${fromCurrency} = ${result} ${toCurrency}`);
        return result;
      }

      console.error(`No conversion path found for ${fromCurrency} -> ${toCurrency}`);
      return amount; // Dönüşüm yapılamadığında orijinal miktarı döndür
    } catch (conversionError) {
      console.error('Conversion error:', conversionError);
      return amount; // Hata durumunda orijinal miktarı döndür
    }
  } catch (error) {
    console.error('Para birimi dönüştürme hatası:', error);
    return amount; // Hata durumunda orijinal miktarı döndür
  }
};

/**
 * Para birimini formatlar
 *
 * @param {number} amount - Miktar
 * @param {string} currency - Para birimi
 * @returns {string} Formatlanmış para
 */
export const formatCurrency = (amount, currency = 'TRY') => {
  if (amount == null) {
    return '';
  }

  const currencySymbols = {
    TRY: '₺',
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    CHF: 'CHF',
    CAD: 'C$',
    AUD: 'A$',
    CNY: '¥',
    RUB: '₽',
    KRW: '₩',
    INR: '₹',
    BRL: 'R$',
    ZAR: 'R',
    SAR: '﷼',
    AED: 'د.إ',
    BTC: '₿',
    ETH: 'Ξ',
  };

  const symbol = currencySymbols[currency] || currency;

  // Sayıyı formatla
  const formattedAmount = new Intl.NumberFormat('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);

  return `${symbol}${formattedAmount}`;
};

/**
 * Döviz kuru geçmişini getirir
 *
 * @param {string} date - Tarih (YYYY-MM-DD formatında)
 * @param {string} baseCurrency - Baz para birimi
 * @returns {Promise<Object>} Geçmiş döviz kurları
 */
export const fetchHistoricalRates = async (date, baseCurrency = 'TRY') => {
  try {
    // Parametreleri kontrol et
    if (!baseCurrency) {
      console.error('Base currency is undefined or null');
      baseCurrency = 'TRY'; // Varsayılan değer
    }

    // API ücretsiz versiyonda geçmiş veriler için farklı bir endpoint kullanıyor
    // Bu örnekte varsayılan API'yi kullanıyoruz, gerçek uygulamada değiştirilmeli
    console.log(`Fetching historical rates for ${baseCurrency} on ${date}`);

    const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${baseCurrency}`);

    if (!response.ok) {
      throw new Error(`Geçmiş döviz kurları alınamadı: ${response.status}`);
    }

    const data = await response.json();

    // API yanıtını kontrol et
    if (!data || !data.rates) {
      console.error('API did not return valid historical data:', data);
      throw new Error('Invalid API response');
    }

    return data;
  } catch (error) {
    console.error('Geçmiş döviz kuru servisi hatası:', error);
    throw error;
  }
};

/**
 * Son güncelleme tarihini döndürür
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} baseCurrency - Baz para birimi
 * @returns {Promise<string>} Son güncelleme tarihi
 */
export const getLastUpdateDate = async (db, baseCurrency = 'TRY') => {
  try {
    const result = await db.getFirstAsync(`
      SELECT MAX(fetch_date) as last_update
      FROM exchange_rates
      WHERE base_currency = ?
    `, [baseCurrency]);

    return result ? result.last_update : null;
  } catch (error) {
    console.error('Son güncelleme tarihi getirme hatası:', error);
    throw error;
  }
};

/**
 * Desteklenen para birimlerini getirir
 *
 * @returns {Array<Object>} Para birimleri listesi
 */
export const getSupportedCurrencies = () => {
  return [
    { code: 'TRY', name: 'Türk Lirası', symbol: '₺' },
    { code: 'USD', name: 'Amerikan Doları', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'İngiliz Sterlini', symbol: '£' },
    { code: 'JPY', name: 'Japon Yeni', symbol: '¥' },
    { code: 'CHF', name: 'İsviçre Frangı', symbol: 'CHF' },
    { code: 'CAD', name: 'Kanada Doları', symbol: 'C$' },
    { code: 'AUD', name: 'Avustralya Doları', symbol: 'A$' },
    { code: 'CNY', name: 'Çin Yuanı', symbol: '¥' },
    { code: 'RUB', name: 'Rus Rublesi', symbol: '₽' },
    { code: 'KRW', name: 'Güney Kore Wonu', symbol: '₩' },
    { code: 'INR', name: 'Hindistan Rupisi', symbol: '₹' },
    { code: 'BRL', name: 'Brezilya Reali', symbol: 'R$' },
    { code: 'ZAR', name: 'Güney Afrika Randı', symbol: 'R' },
    { code: 'BTC', name: 'Bitcoin', symbol: '₿' },
    { code: 'ETH', name: 'Ethereum', symbol: 'Ξ' },
  ];
};

/**
 * Varsayılan para birimini getirir
 *
 * @returns {string} Varsayılan para birimi kodu
 */
export const getDefaultCurrency = () => 'TRY';

/**
 * Döviz kurlarını günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Array<string>} currencies - Güncellenecek para birimleri
 * @returns {Promise<Object>} Güncellenen kurlar
 */
export const updateExchangeRates = async (db, currencies = ['TRY', 'USD', 'EUR']) => {
  try {
    // Parametreleri kontrol et
    if (!db) {
      throw new Error('Database connection is required');
    }

    if (!currencies || !Array.isArray(currencies) || currencies.length === 0) {
      console.warn('Invalid currencies array, using default');
      currencies = ['TRY', 'USD', 'EUR'];
    }

    const today = new Date().toISOString().split('T')[0];
    const results = {};

    console.log(`Updating exchange rates for ${currencies.join(', ')} on ${today}`);

    for (const currency of currencies) {
      try {
        console.log(`Fetching rates for ${currency}`);
        const apiRates = await fetchExchangeRates(currency);

        if (apiRates && apiRates.rates) {
          console.log(`Saving rates for ${currency}`);
          await saveExchangeRates(db, apiRates.rates, currency, today);
          results[currency] = apiRates.rates;
        } else {
          console.error(`Failed to get valid rates for ${currency}`);
        }
      } catch (currencyError) {
        console.error(`Error updating rates for ${currency}:`, currencyError);
        // Continue with other currencies
      }
    }

    return results;
  } catch (error) {
    console.error('Döviz kurları güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Altın fiyatlarını getirir (örnek)
 * Not: Gerçek bir API kullanılmalıdır
 *
 * @returns {Promise<Object>} Altın fiyatları
 */
export const fetchGoldRates = async () => {
  try {
    // Örnek veri - gerçek uygulamada bir API kullanılmalıdır
    return {
      date: new Date().toISOString(),
      base: 'XAU', // Altın
      rates: {
        TRY: 2350.75, // 1 gram altın = 2350.75 TL
        USD: 65.42,   // 1 gram altın = 65.42 USD
        EUR: 60.18,   // 1 gram altın = 60.18 EUR
      }
    };
  } catch (error) {
    console.error('Altın fiyatları servisi hatası:', error);
    throw error;
  }
};

/**
 * Kripto para fiyatlarını getirir (örnek)
 * Not: Gerçek bir API kullanılmalıdır
 *
 * @returns {Promise<Object>} Kripto para fiyatları
 */

/**
 * Bir tutarın döviz karşılıklarını hesaplar ve döndürür
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} amount - Miktar
 * @param {string} currency - Para birimi
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @param {string} customCurrency - Özel para birimi
 * @returns {Promise<Object>} Döviz karşılıkları
 */
export const calculateCurrencyEquivalents = async (db, amount, currency = 'TRY', date = null, customCurrency = null) => {
  try {
    // Parametreleri kontrol et
    if (!db) {
      throw new Error('Database connection is required');
    }

    if (amount == null || isNaN(amount)) {
      console.error('Invalid amount:', amount);
      return {
        USD: 0,
        EUR: 0,
        custom: { currency: customCurrency || 'GBP', amount: 0 },
        original: { amount, currency }
      };
    }

    if (!currency) {
      console.error('Currency is undefined or null');
      currency = 'TRY'; // Varsayılan değer
    }

    // Tarih belirtilmemişse bugünün tarihini kullan
    const targetDate = date || new Date().toISOString().split('T')[0];

    // Özel para birimi belirtilmemişse, ayarlardan al
    if (!customCurrency) {
      try {
        // settingsService'i dinamik olarak import et
        const settingsService = await import('./settingsService');
        customCurrency = await settingsService.getCustomCurrency(db);
      } catch (error) {
        console.error('Özel para birimi getirme hatası:', error);
        customCurrency = 'GBP'; // Varsayılan değer
      }
    }

    // Sadece USD, EUR ve özel para birimi karşılıklarını hesapla
    let usdAmount = 0;
    let eurAmount = 0;
    let customAmount = 0;

    // USD karşılığını hesapla (eğer para birimi USD değilse)
    if (currency !== 'USD') {
      usdAmount = await convertCurrency(db, amount, currency, 'USD', targetDate);
    } else {
      usdAmount = amount; // Zaten USD ise doğrudan miktarı kullan
    }

    // EUR karşılığını hesapla (eğer para birimi EUR değilse)
    if (currency !== 'EUR') {
      eurAmount = await convertCurrency(db, amount, currency, 'EUR', targetDate);
    } else {
      eurAmount = amount; // Zaten EUR ise doğrudan miktarı kullan
    }

    // Özel para birimi karşılığını hesapla
    if (customCurrency && customCurrency !== currency && customCurrency !== 'USD' && customCurrency !== 'EUR') {
      customAmount = await convertCurrency(db, amount, currency, customCurrency, targetDate);
    } else if (customCurrency === currency) {
      customAmount = amount; // Zaten özel para birimi ise doğrudan miktarı kullan
    }

    return {
      USD: usdAmount,
      EUR: eurAmount,
      custom: { currency: customCurrency, amount: customAmount },
      original: { amount, currency, date: targetDate }
    };
  } catch (error) {
    console.error('Döviz karşılıkları hesaplama hatası:', error);
    return {
      USD: 0,
      EUR: 0,
      custom: { currency: customCurrency || 'GBP', amount: 0 },
      original: { amount, currency }
    };
  }
};

/**
 * Bir finansal işlemin döviz karşılıklarını kaydeder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} tableName - Tablo adı
 * @param {number} recordId - Kayıt ID'si
 * @param {number} amount - Miktar
 * @param {string} currency - Para birimi
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @param {string} customCurrency - Özel para birimi
 * @returns {Promise<boolean>} Başarılı mı?
 */
export const saveCurrencyEquivalents = async (db, tableName, recordId, amount, currency = 'TRY', date = null, customCurrency = null) => {
  try {
    // Parametreleri kontrol et
    if (!db) {
      throw new Error('Database connection is required');
    }

    if (!tableName || !recordId) {
      throw new Error('Table name and record ID are required');
    }

    if (amount == null || isNaN(amount)) {
      console.error('Invalid amount:', amount);
      return false;
    }

    // Özel para birimini al
    let userCustomCurrency = customCurrency || 'GBP';
    if (!userCustomCurrency) {
      try {
        // settingsService'i dinamik olarak import et
        const settingsService = await import('./settingsService');
        userCustomCurrency = await settingsService.getCustomCurrency(db);
      } catch (error) {
        console.error('Özel para birimi getirme hatası:', error);
        userCustomCurrency = 'GBP';
      }
    }

    // Döviz karşılıklarını hesapla
    const equivalents = await calculateCurrencyEquivalents(db, amount, currency, date, userCustomCurrency);

    // Tablo adına göre güncelleme sorgusunu belirle
    let updateQuery = '';
    let params = [];

    // Tüm tablolar için aynı sorgu yapısını kullan
    updateQuery = `
      UPDATE ${tableName}
      SET usd_equivalent = ?, eur_equivalent = ?,
          custom_currency = ?, custom_equivalent = ?
      WHERE id = ?
    `;

    params = [
      equivalents.USD,
      equivalents.EUR,
      equivalents.custom.currency,
      equivalents.custom.amount,
      recordId
    ];

    // Güncelleme sorgusunu çalıştır
    try {
      const result = await db.runAsync(updateQuery, params);
      return result.changes > 0;
    } catch (dbError) {
      console.error(`${tableName} tablosunu güncelleme hatası:`, dbError);

      // Sütunlar yoksa, tek tek güncellemeyi dene
      try {
        // Önce sütunların varlığını kontrol et
        const columns = await db.getAllAsync(`PRAGMA table_info(${tableName})`);
        const columnNames = columns.map(col => col.name);

        // Sadece var olan sütunları güncelle
        let updateFields = [];
        let updateParams = [];

        if (columnNames.includes('usd_equivalent')) {
          updateFields.push('usd_equivalent = ?');
          updateParams.push(equivalents.USD);
        }

        if (columnNames.includes('eur_equivalent')) {
          updateFields.push('eur_equivalent = ?');
          updateParams.push(equivalents.EUR);
        }

        if (columnNames.includes('custom_currency')) {
          updateFields.push('custom_currency = ?');
          updateParams.push(equivalents.custom.currency);
        }

        if (columnNames.includes('custom_equivalent')) {
          updateFields.push('custom_equivalent = ?');
          updateParams.push(equivalents.custom.amount);
        }

        // Güncelleme yapılacak alan yoksa başarılı döndür
        if (updateFields.length === 0) {
          console.log(`${tableName} tablosunda güncellenecek döviz alanı bulunamadı.`);
          return true;
        }

        // ID'yi ekle
        updateParams.push(recordId);

        // Güncelleme sorgusunu oluştur
        const fallbackQuery = `
          UPDATE ${tableName}
          SET ${updateFields.join(', ')}
          WHERE id = ?
        `;

        // Sorguyu çalıştır
        const fallbackResult = await db.runAsync(fallbackQuery, updateParams);
        return fallbackResult.changes > 0;
      } catch (fallbackError) {
        console.error(`${tableName} tablosunu alternatif güncelleme hatası:`, fallbackError);
        return false;
      }
    }
  } catch (error) {
    console.error('Döviz karşılıkları kaydetme hatası:', error);
    return false;
  }
};

/**
 * Kripto para fiyatlarını getirir (örnek)
 * Not: Gerçek bir API kullanılmalıdır
 *
 * @returns {Promise<Object>} Kripto para fiyatları
 */
export const fetchCryptoRates = async () => {
  try {
    // Örnek veri - gerçek uygulamada bir API kullanılmalıdır
    return {
      date: new Date().toISOString(),
      base: 'USD',
      rates: {
        BTC: 0.000016, // 1 USD = 0.000016 BTC
        ETH: 0.00031,  // 1 USD = 0.00031 ETH
        XRP: 0.42,     // 1 USD = 0.42 XRP
        LTC: 0.0074,   // 1 USD = 0.0074 LTC
        ADA: 0.33,     // 1 USD = 0.33 ADA
      }
    };
  } catch (error) {
    console.error('Kripto para fiyatları servisi hatası:', error);
    throw error;
  }
};


