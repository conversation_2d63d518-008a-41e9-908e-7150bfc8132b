import AsyncStorage from '@react-native-async-storage/async-storage';
import * as LocalAuthentication from 'expo-local-authentication';

// AsyncStorage anahtarları
const PIN_KEY = 'user_pin_code';
const PIN_STATUS_KEY = 'pin_setup_status';
const PIN_ATTEMPTS_KEY = 'pin_attempt_count';
const PIN_LOCKOUT_TIME_KEY = 'pin_lockout_time';
const BIOMETRIC_ENABLED_KEY = 'biometric_enabled';
const MAX_ATTEMPTS = 5;
const LOCKOUT_DURATION = 5 * 60 * 1000; // 5 dakika (milisaniye cinsinden)

/**
 * PIN ile ilgili işlemleri yöneten servis
 */
export const pinService = {
  /**
   * PIN kodunu kaydeder
   *
   * @param {string} pin - Kaydedilecek PIN kodu
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  savePin: async (pin) => {
    try {
      await AsyncStorage.setItem(PIN_KEY, pin);
      await AsyncStorage.setItem(PIN_STATUS_KEY, 'true');

      // PIN kodu başarıyla kaydedildiğinde, deneme sayısını sıfırla
      await AsyncStorage.removeItem(PIN_ATTEMPTS_KEY);
      await AsyncStorage.removeItem(PIN_LOCKOUT_TIME_KEY);

      return true;
    } catch (error) {
      console.error('PIN kodu kaydetme hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodunu doğrular
   *
   * @param {string} pin - Doğrulanacak PIN kodu
   * @returns {Promise<boolean>} PIN kodu doğruysa true, aksi halde false
   */
  verifyPin: async (pin) => {
    try {
      // Kilitlenme durumunu kontrol et
      const isLocked = await pinService.isPinLocked();
      if (isLocked) {
        return false;
      }

      const storedPin = await AsyncStorage.getItem(PIN_KEY);
      const isValid = pin === storedPin;

      if (isValid) {
        // PIN kodu doğruysa, deneme sayısını sıfırla
        await AsyncStorage.removeItem(PIN_ATTEMPTS_KEY);
        await AsyncStorage.removeItem(PIN_LOCKOUT_TIME_KEY);
      } else {
        // PIN kodu yanlışsa, deneme sayısını artır
        await pinService.incrementAttempts();
      }

      return isValid;
    } catch (error) {
      console.error('PIN kodu doğrulama hatası:', error);
      return false;
    }
  },

  /**
   * PIN durumunu kontrol eder
   * @returns {Promise<boolean>} PIN kurulu mu
   */
  isPinSet: async () => {
    try {
      const status = await AsyncStorage.getItem(PIN_STATUS_KEY);
      return status === 'true';
    } catch (error) {
      console.error('PIN durum kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodunun ayarlanıp ayarlanmadığını kontrol eder
   * @returns {Promise<boolean>} PIN kodu ayarlanmış mı
   */
  hasPin: async () => {
    try {
      const pin = await AsyncStorage.getItem(PIN_KEY);
      return pin !== null && pin !== '';
    } catch (error) {
      console.error('PIN kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodunun kilitli olup olmadığını kontrol eder
   *
   * @returns {Promise<boolean>} PIN kodu kilitlendiyse true, aksi halde false
   */
  isPinLocked: async () => {
    try {
      const lockoutTimeStr = await AsyncStorage.getItem(PIN_LOCKOUT_TIME_KEY);

      if (!lockoutTimeStr) {
        return false;
      }

      const lockoutTime = parseInt(lockoutTimeStr, 10);
      const now = Date.now();

      // Kilitlenme süresi geçtiyse, kilidi kaldır
      if (now > lockoutTime) {
        await AsyncStorage.removeItem(PIN_LOCKOUT_TIME_KEY);
        await AsyncStorage.removeItem(PIN_ATTEMPTS_KEY);
        return false;
      }

      return true;
    } catch (error) {
      console.error('PIN kilidi kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodu kilitlenme süresini getirir
   *
   * @returns {Promise<number>} Kilitlenme süresi (milisaniye cinsinden)
   */
  getLockoutTime: async () => {
    try {
      const lockoutTimeStr = await AsyncStorage.getItem(PIN_LOCKOUT_TIME_KEY);

      if (!lockoutTimeStr) {
        return 0;
      }

      const lockoutTime = parseInt(lockoutTimeStr, 10);
      const now = Date.now();
      const remainingTime = Math.max(0, lockoutTime - now);

      return remainingTime;
    } catch (error) {
      console.error('PIN kilidi süresi getirme hatası:', error);
      return 0;
    }
  },

  /**
   * PIN kodu deneme sayısını artırır
   *
   * @returns {Promise<number>} Güncel deneme sayısı
   */
  incrementAttempts: async () => {
    try {
      const attemptsStr = await AsyncStorage.getItem(PIN_ATTEMPTS_KEY);
      const attempts = attemptsStr ? parseInt(attemptsStr, 10) : 0;
      const newAttempts = attempts + 1;

      await AsyncStorage.setItem(PIN_ATTEMPTS_KEY, newAttempts.toString());

      // Maksimum deneme sayısına ulaşıldıysa, kilitlenme zamanını ayarla
      if (newAttempts >= MAX_ATTEMPTS) {
        const lockoutTime = Date.now() + LOCKOUT_DURATION;
        await AsyncStorage.setItem(PIN_LOCKOUT_TIME_KEY, lockoutTime.toString());
      }

      return newAttempts;
    } catch (error) {
      console.error('PIN deneme sayısı artırma hatası:', error);
      return 0;
    }
  },

  /**
   * Kalan deneme sayısını getirir
   *
   * @returns {Promise<number>} Kalan deneme sayısı
   */
  getRemainingAttempts: async () => {
    try {
      const attemptsStr = await AsyncStorage.getItem(PIN_ATTEMPTS_KEY);
      const attempts = attemptsStr ? parseInt(attemptsStr, 10) : 0;

      return Math.max(0, MAX_ATTEMPTS - attempts);
    } catch (error) {
      console.error('Kalan deneme sayısı getirme hatası:', error);
      return MAX_ATTEMPTS;
    }
  },

  /**
   * PIN kodunu değiştirir
   *
   * @param {string} currentPin - Mevcut PIN kodu
   * @param {string} newPin - Yeni PIN kodu
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  changePin: async (currentPin, newPin) => {
    try {
      // Mevcut PIN'i doğrula
      const isValid = await pinService.verifyPin(currentPin);
      if (!isValid) return false;

      // Yeni PIN'i kaydet
      await AsyncStorage.setItem(PIN_KEY, newPin);

      return true;
    } catch (error) {
      console.error('PIN kodu değiştirme hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodunu siler
   *
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  removePin: async () => {
    try {
      await AsyncStorage.removeItem(PIN_KEY);
      await AsyncStorage.removeItem(PIN_STATUS_KEY);
      await AsyncStorage.removeItem(PIN_ATTEMPTS_KEY);
      await AsyncStorage.removeItem(PIN_LOCKOUT_TIME_KEY);

      return true;
    } catch (error) {
      console.error('PIN kodu silme hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodunu ayarlar (yeni PIN oluşturma)
   *
   * @param {string} pin - Ayarlanacak PIN kodu
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  setPin: async (pin) => {
    try {
      await AsyncStorage.setItem(PIN_KEY, pin);
      await AsyncStorage.setItem(PIN_STATUS_KEY, 'enabled');

      // PIN kodu başarıyla kaydedildiğinde, deneme sayısını sıfırla
      await AsyncStorage.removeItem(PIN_ATTEMPTS_KEY);
      await AsyncStorage.removeItem(PIN_LOCKOUT_TIME_KEY);

      console.log('PIN başarıyla ayarlandı');
      return true;
    } catch (error) {
      console.error('PIN kodu ayarlama hatası:', error);
      return false;
    }
  },

  /**
   * Güvenlik sorularını kaydet
   *
   * @param {Object} answers - Güvenlik soruları cevapları
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  setSecurityAnswers: async (answers) => {
    try {
      await AsyncStorage.setItem('security_answers', JSON.stringify(answers));
      return true;
    } catch (error) {
      console.error('Güvenlik soruları kaydetme hatası:', error);
      return false;
    }
  },

  /**
   * Güvenlik sorularını getir
   *
   * @returns {Promise<Object|null>} Güvenlik soruları cevapları
   */
  getSecurityAnswers: async () => {
    try {
      const answers = await AsyncStorage.getItem('security_answers');
      return answers ? JSON.parse(answers) : null;
    } catch (error) {
      console.error('Güvenlik soruları getirme hatası:', error);
      return null;
    }
  },

  /**
   * PIN kodu etkin mi kontrol eder
   * @returns {Promise<boolean>} PIN kodu etkin mi
   */
  isPinEnabled: async () => {
    try {
      return await pinService.isPinSet();
    } catch (error) {
      console.error('PIN etkinlik kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodunu etkinleştirir
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  enablePin: async () => {
    try {
      await AsyncStorage.setItem(PIN_STATUS_KEY, 'true');
      return true;
    } catch (error) {
      console.error('PIN etkinleştirme hatası:', error);
      return false;
    }
  },

  /**
   * PIN kodunu devre dışı bırakır
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  disablePin: async () => {
    try {
      await AsyncStorage.setItem(PIN_STATUS_KEY, 'false');
      return true;
    } catch (error) {
      console.error('PIN devre dışı bırakma hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulama etkin mi kontrol eder
   * @returns {Promise<boolean>} Biyometrik kimlik doğrulama etkin mi
   */
  isBiometricEnabled: async () => {
    try {
      const value = await AsyncStorage.getItem(BIOMETRIC_ENABLED_KEY);
      return value === 'true';
    } catch (error) {
      console.error('Biyometrik etkinlik kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulama etkinliğini ayarlar
   * @param {boolean} enabled Etkinleştirme durumu
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  setBiometricEnabled: async (enabled) => {
    try {
      await AsyncStorage.setItem(BIOMETRIC_ENABLED_KEY, enabled ? 'true' : 'false');
      return true;
    } catch (error) {
      console.error('Biyometrik etkinlik ayarlama hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulama yapılabilir mi kontrol eder
   * @returns {Promise<boolean>} Biyometrik kimlik doğrulama yapılabilir mi
   */
  isBiometricAvailable: async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && isEnrolled;
    } catch (error) {
      console.error('Biyometrik donanım kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulama yapar
   * @param {string} promptMessage Kullanıcıya gösterilecek mesaj
   * @returns {Promise<boolean>} Kimlik doğrulama başarılı mı
   */
  authenticateWithBiometrics: async (promptMessage = 'Kimliğinizi doğrulayın') => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        disableDeviceFallback: false,
        cancelLabel: 'İptal',
      });
      return result.success;
    } catch (error) {
      console.error('Biyometrik kimlik doğrulama hatası:', error);
      return false;
    }
  }
};
