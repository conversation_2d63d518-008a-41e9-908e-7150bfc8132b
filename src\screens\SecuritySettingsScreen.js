import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';

/**
 * Güvenlik Ayarları Ekranı
 * PIN, biometrik ve otomatik kilit ayarları
 */
export default function SecuritySettingsScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  const { 
    authSettings, 
    setPin, 
    removePin, 
    setBiometric, 
    setAutoLock, 
    checkBiometricSupport,
    loadAuthSettings
  } = useAuth();

  const [biometricSupport, setBiometricSupport] = useState({
    hasHardware: false,
    isEnrolled: false,
    supportedTypes: [],
    isSupported: false
  });

  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    const support = await checkBiometricSupport();
    setBiometricSupport(support);
  };

  // PIN ayarları
  const handlePinToggle = async (enabled) => {
    if (enabled) {
      // PIN kurulumu için ekrana git
      navigation.navigate('PinAuth', { mode: 'setup' });
    } else {
      // PIN kaldırma onayı
      Alert.alert(
        'PIN Kaldır',
        'PIN korumasını kaldırmak istediğinizden emin misiniz?',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Kaldır',
            style: 'destructive',
            onPress: async () => {
              const result = await removePin();
              if (result.success) {
                await loadAuthSettings();
                Alert.alert('Başarılı', 'PIN koruması kaldırıldı');
              } else {
                Alert.alert('Hata', result.error);
              }
            }
          }
        ]
      );
    }
  };

  // PIN değiştir
  const handleChangePin = () => {
    navigation.navigate('PinAuth', { mode: 'change' });
  };

  // Biometrik ayarları
  const handleBiometricToggle = async (enabled) => {
    if (!biometricSupport.isSupported) {
      Alert.alert(
        'Biometrik Kimlik Doğrulama',
        'Cihazınız biometrik kimlik doğrulamayı desteklemiyor veya kayıtlı biometrik veri bulunmuyor.'
      );
      return;
    }

    const result = await setBiometric(enabled);
    if (result.success) {
      await loadAuthSettings();
      Alert.alert(
        'Başarılı', 
        enabled ? 'Biometrik kimlik doğrulama etkinleştirildi' : 'Biometrik kimlik doğrulama devre dışı bırakıldı'
      );
    } else {
      Alert.alert('Hata', result.error);
    }
  };

  // Otomatik kilit ayarları
  const handleAutoLockToggle = async (enabled) => {
    const result = await setAutoLock(enabled, authSettings.autoLockTime);
    if (result.success) {
      await loadAuthSettings();
    } else {
      Alert.alert('Hata', result.error);
    }
  };

  // Otomatik kilit süresi
  const handleAutoLockTimeChange = (minutes) => {
    Alert.alert(
      'Otomatik Kilit Süresi',
      'Uygulamanın ne kadar süre sonra kilitlenmesini istiyorsunuz?',
      [
        { text: '1 Dakika', onPress: () => updateAutoLockTime(1) },
        { text: '5 Dakika', onPress: () => updateAutoLockTime(5) },
        { text: '10 Dakika', onPress: () => updateAutoLockTime(10) },
        { text: '30 Dakika', onPress: () => updateAutoLockTime(30) },
        { text: 'İptal', style: 'cancel' }
      ]
    );
  };

  const updateAutoLockTime = async (minutes) => {
    const result = await setAutoLock(authSettings.autoLockEnabled, minutes);
    if (result.success) {
      await loadAuthSettings();
    } else {
      Alert.alert('Hata', result.error);
    }
  };

  // Biometrik türü metni
  const getBiometricTypeText = () => {
    if (!biometricSupport.supportedTypes.length) return 'Biometrik';
    
    const types = biometricSupport.supportedTypes;
    if (types.includes(1)) return 'Parmak İzi'; // FINGERPRINT
    if (types.includes(2)) return 'Yüz Tanıma'; // FACIAL_RECOGNITION
    if (types.includes(3)) return 'İris Tanıma'; // IRIS
    return 'Biometrik';
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Güvenlik</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        {/* PIN Ayarları */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>PIN Koruması</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="lock" size={24} color={theme.PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>PIN Kullan</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  4 haneli PIN ile uygulamayı koruyun
                </Text>
              </View>
            </View>
            <Switch
              value={authSettings.pinEnabled}
              onValueChange={handlePinToggle}
              trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
              thumbColor={theme.WHITE}
            />
          </View>

          {authSettings.pinEnabled && (
            <TouchableOpacity style={styles.settingItem} onPress={handleChangePin}>
              <View style={styles.settingLeft}>
                <MaterialIcons name="edit" size={24} color={theme.PRIMARY} />
                <View style={styles.settingText}>
                  <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>PIN Değiştir</Text>
                  <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                    Mevcut PIN'inizi değiştirin
                  </Text>
                </View>
              </View>
              <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          )}

          {authSettings.pinEnabled && (
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => navigation.navigate('PinReset')}
            >
              <View style={styles.settingLeft}>
                <MaterialIcons name="lock-reset" size={24} color={Colors.WARNING} />
                <View style={styles.settingText}>
                  <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>PIN Unuttum</Text>
                  <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                    Güvenlik soruları ile PIN sıfırla
                  </Text>
                </View>
              </View>
              <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          )}
        </View>

        {/* Biometrik Ayarları */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Biometrik Kimlik Doğrulama</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="fingerprint" size={24} color={theme.PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>
                  {getBiometricTypeText()}
                </Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  {biometricSupport.isSupported 
                    ? 'Biometrik verilerinizle hızlı giriş yapın'
                    : 'Cihazınızda desteklenmiyor'
                  }
                </Text>
              </View>
            </View>
            <Switch
              value={authSettings.biometricEnabled}
              onValueChange={handleBiometricToggle}
              disabled={!biometricSupport.isSupported}
              trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
              thumbColor={theme.WHITE}
            />
          </View>
        </View>

        {/* Otomatik Kilit Ayarları */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Otomatik Kilit</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="timer" size={24} color={theme.PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Otomatik Kilit</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Belirli süre sonra uygulamayı kilitle
                </Text>
              </View>
            </View>
            <Switch
              value={authSettings.autoLockEnabled}
              onValueChange={handleAutoLockToggle}
              trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
              thumbColor={theme.WHITE}
            />
          </View>

          {authSettings.autoLockEnabled && (
            <TouchableOpacity style={styles.settingItem} onPress={handleAutoLockTimeChange}>
              <View style={styles.settingLeft}>
                <MaterialIcons name="schedule" size={24} color={theme.PRIMARY} />
                <View style={styles.settingText}>
                  <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Kilit Süresi</Text>
                  <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                    {authSettings.autoLockTime} dakika sonra kilitle
                  </Text>
                </View>
              </View>
              <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          )}
        </View>

        {/* Gelişmiş Güvenlik Ayarları */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Gelişmiş Güvenlik</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="security" size={24} color={theme.PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Maksimum Deneme</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  {authSettings.maxAttempts || 5} hatalı deneme sonrası kilitle
                </Text>
              </View>
            </View>
            <TouchableOpacity onPress={() => Alert.alert('Bilgi', 'Bu özellik yakında gelecek')}>
              <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="lock-clock" size={24} color={theme.PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Kilitleme Süresi</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  {authSettings.lockoutTime || 30} saniye kilitle
                </Text>
              </View>
            </View>
            <TouchableOpacity onPress={() => Alert.alert('Bilgi', 'Bu özellik yakında gelecek')}>
              <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="history" size={24} color={theme.PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Güvenlik Logları</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Güvenlik olaylarını kaydet ve göster
                </Text>
              </View>
            </View>
            <Switch
              value={authSettings.showSecurityLogs || false}
              onValueChange={() => Alert.alert('Bilgi', 'Bu özellik yakında gelecek')}
              trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
              thumbColor={theme.WHITE}
            />
          </View>
        </View>

        {/* Güvenlik Bilgileri */}
        <View style={[styles.infoSection, { backgroundColor: theme.CARD }]}>
          <MaterialIcons name="info" size={24} color={Colors.INFO} />
          <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
            Güvenlik ayarları cihazınızda yerel olarak saklanır. PIN ve biometrik verileriniz güvenli bir şekilde şifrelenir.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 12,
    flex: 1,
  },
});
