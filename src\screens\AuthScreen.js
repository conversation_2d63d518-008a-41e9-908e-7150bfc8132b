import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { pinService } from '../services/pinService';
import * as LocalAuthentication from 'expo-local-authentication';

/**
 * <PERSON><PERSON> doğrulama ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 */
const AuthScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const [isPinEnabled, setIsPinEnabled] = useState(false);
  const [isBiometricAvailable, setIsBiometricAvailable] = useState(false);
  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);

  // Biyometrik kimlik doğrulama kontrolü
  const checkBiometric = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      setIsBiometricAvailable(hasHardware && isEnrolled);

      if (hasHardware && isEnrolled) {
        try {
          const biometricEnabled = await pinService.isBiometricEnabled();
          setIsBiometricEnabled(biometricEnabled);

          if (biometricEnabled) {
            authenticateWithBiometric();
          }
        } catch (error) {
          console.error('Biyometrik etkinlik kontrolü hatası:', error);
        }
      }
    } catch (error) {
      console.error('Biyometrik kontrol hatası:', error);
    }
  };

  // PIN kontrolü
  const checkPin = async () => {
    try {
      const pinEnabled = await pinService.isPinEnabled();
      setIsPinEnabled(pinEnabled);

      if (!pinEnabled) {
        // PIN ayarlanmamış, doğrudan ana ekrana git
        console.log('PIN ayarlanmamış, ana ekrana yönlendiriliyor...');
        navigation.replace('Main');
      } else {
        console.log('PIN etkin, kimlik doğrulama gerekli');
      }
    } catch (error) {
      console.error('PIN kontrol hatası:', error);
      // Development mode'da hata durumunda da ana ekrana git
      console.log('PIN kontrol hatası, ana ekrana yönlendiriliyor...');
      navigation.replace('Main');
    }
  };

  // İlk yükleme
  useEffect(() => {
    checkPin();
    checkBiometric();
  }, []);

  // Biyometrik kimlik doğrulama
  const authenticateWithBiometric = async () => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Kimliğinizi doğrulayın',
        cancelLabel: 'İptal',
        disableDeviceFallback: true,
      });

      if (result.success) {
        navigation.replace('Main');
      }
    } catch (error) {
      console.error('Biyometrik kimlik doğrulama hatası:', error);
    }
  };

  // PIN ile giriş
  const loginWithPin = () => {
    navigation.navigate('Pin', { mode: 'verify' });
  };

  // PIN oluştur
  const setupPin = () => {
    navigation.navigate('Pin', { mode: 'setup' });
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.content}>
        <Image
          source={require('../../assets/icon.png')}
          style={styles.logo}
          resizeMode="contain"
        />

        <Text style={styles.title}>Finansal Takip</Text>
        <Text style={styles.subtitle}>Finansal durumunuzu takip edin ve kontrol altında tutun</Text>

        {isPinEnabled ? (
          <View style={styles.authOptions}>
            <TouchableOpacity
              style={styles.authButton}
              onPress={loginWithPin}
            >
              <MaterialIcons name="pin" size={24} color="#fff" />
              <Text style={styles.authButtonText}>PIN ile Giriş Yap</Text>
            </TouchableOpacity>

            {isBiometricAvailable && (
              <TouchableOpacity
                style={[styles.authButton, styles.biometricButton]}
                onPress={authenticateWithBiometric}
              >
                <MaterialIcons name="fingerprint" size={24} color="#fff" />
                <Text style={styles.authButtonText}>Biyometrik Giriş</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.setupContainer}>
            <Text style={styles.setupText}>
              Uygulamayı güvenli hale getirmek için bir PIN kodu oluşturun
            </Text>

            <TouchableOpacity
              style={styles.setupButton}
              onPress={setupPin}
            >
              <Text style={styles.setupButtonText}>PIN Kodu Oluştur</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.skipButton}
              onPress={() => navigation.replace('Main')}
            >
              <Text style={styles.skipButtonText}>Şimdilik Geç</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 48,
    textAlign: 'center',
  },
  authOptions: {
    width: '100%',
  },
  authButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    paddingVertical: 16,
    marginBottom: 16,
  },
  biometricButton: {
    backgroundColor: '#3498db',
  },
  authButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
  setupContainer: {
    width: '100%',
    alignItems: 'center',
  },
  setupText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  setupButton: {
    width: '100%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  setupButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  skipButton: {
    paddingVertical: 8,
  },
  skipButtonText: {
    fontSize: 14,
    color: '#999',
  },
});

export default AuthScreen;
